# YendorCats Security Setup Guide

## 🔐 Environment Configuration

### Required Environment Variables

Create a `.env` file in the project root with the following variables:

```bash
# Database Configuration
MYSQL_ROOT_PASSWORD=your_secure_root_password_here
MYSQL_USER=yendorcats_user
MYSQL_PASSWORD=your_secure_db_password_here

# JWT Configuration (minimum 32 characters)
YENDOR_JWT_SECRET=your_very_long_and_secure_jwt_secret_key_at_least_32_characters_long

# AWS/Backblaze B2 Configuration
AWS_S3_ACCESS_KEY=your_b2_access_key_here
AWS_S3_SECRET_KEY=your_b2_secret_key_here
AWS_S3_BUCKET_NAME=your_bucket_name

# Vault Configuration (if using HashiCorp Vault)
VAULT_TOKEN=your_vault_token_here
VAULT_ADDR=http://localhost:8200

# Development Settings
ASPNETCORE_ENVIRONMENT=Development
```

### Configuration Files Setup

1. **Copy template files:**
   ```bash
   cp backend/YendorCats.API/appsettings.json.template backend/YendorCats.API/appsettings.json
   cp backend/YendorCats.API/appsettings.Development.json.template backend/YendorCats.API/appsettings.Development.json
   cp .env.template .env
   ```

2. **Edit the copied files** with your actual values (never commit these!)

### Security Best Practices

1. **Never commit sensitive files:**
   - `.env` files
   - `appsettings.Development.json`
   - Any files containing API keys, passwords, or tokens

2. **Use environment variables** for all sensitive configuration

3. **Rotate credentials regularly:**
   - JWT secrets
   - Database passwords
   - API keys

4. **Use strong passwords:**
   - Minimum 16 characters
   - Mix of letters, numbers, and symbols

### Vault Integration (Optional)

For production environments, consider using HashiCorp Vault:

1. Start Vault server
2. Initialize and unseal Vault
3. Store secrets in Vault
4. Configure application to read from Vault

## 🚨 Security Incident Response

If credentials are accidentally committed:

1. **Immediately rotate all exposed credentials**
2. **Remove sensitive data from git history**
3. **Update .gitignore to prevent future incidents**
4. **Audit access logs for unauthorized usage**

## 📋 Security Checklist

- [ ] `.env` file created and configured
- [ ] All template files copied and customized
- [ ] No sensitive data in git repository
- [ ] Strong passwords and secrets generated
- [ ] .gitignore properly configured
- [ ] Regular credential rotation scheduled
