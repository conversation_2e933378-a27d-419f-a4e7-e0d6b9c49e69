using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using YendorCats.API.Configuration;
using YendorCats.API.Data.Repositories;
using YendorCats.API.Models;
using YendorCats.API.Models.DTOs;
using YendorCats.API.Services.Migration;
using YendorCats.API.Services.Compatibility;
using System.Collections.Concurrent;
using System.Threading;
using System.Diagnostics;
using System.Text.Json;

namespace YendorCats.API.Services.Migration
{
    /// <summary>
    /// Service implementation for migrating S3 metadata to database with dual storage support
    /// Provides robust batch processing, progress tracking, and error handling
    /// </summary>
    public class S3ToDbMigrationService : IS3ToDbMigrationService
    {
        private readonly IGalleryRepository _galleryRepository;
        private readonly IS3StorageService _s3StorageService;
        private readonly ILogger<S3ToDbMigrationService> _logger;
        private readonly StorageProviderConfiguration _storageConfig;
        
        // In-memory tracking for active migrations
        private readonly ConcurrentDictionary<string, MigrationContext> _activeMigrations = new();
        private readonly ConcurrentDictionary<string, CancellationTokenSource> _migrationCancellationTokens = new();
        
        public S3ToDbMigrationService(
            IGalleryRepository galleryRepository,
            IS3StorageService s3StorageService,
            ILogger<S3ToDbMigrationService> logger,
            IOptions<StorageProviderConfiguration> storageConfig)
        {
            _galleryRepository = galleryRepository;
            _s3StorageService = s3StorageService;
            _logger = logger;
            _storageConfig = storageConfig.Value;
        }

        public async Task<string> StartMigrationAsync(int batchSize = 100, bool enableB2Sync = true, bool dryRun = false)
        {
            var migrationId = Guid.NewGuid().ToString();
            var cancellationTokenSource = new CancellationTokenSource();
            
            _logger.LogInformation("Starting migration {MigrationId} with batch size {BatchSize}, B2 sync: {EnableB2Sync}, dry run: {DryRun}", 
                migrationId, batchSize, enableB2Sync, dryRun);

            try
            {
                // Get total count of S3 objects to migrate
                var s3Objects = await _s3StorageService.ListObjectsAsync();
                var totalItems = s3Objects.Count;
                
                var migrationContext = new MigrationContext
                {
                    MigrationId = migrationId,
                    Status = "Running",
                    StartTime = DateTime.UtcNow,
                    TotalItems = totalItems,
                    BatchSize = batchSize,
                    EnableB2Sync = enableB2Sync,
                    IsDryRun = dryRun,
                    S3Objects = s3Objects
                };

                _activeMigrations[migrationId] = migrationContext;
                _migrationCancellationTokens[migrationId] = cancellationTokenSource;

                // Start migration process in background
                _ = Task.Run(async () => await ExecuteMigrationAsync(migrationContext, cancellationTokenSource.Token));

                return migrationId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to start migration {MigrationId}", migrationId);
                throw;
            }
        }

        public async Task<bool> ResumeMigrationAsync(string migrationId)
        {
            if (!_activeMigrations.TryGetValue(migrationId, out var context))
            {
                _logger.LogWarning("Migration {MigrationId} not found for resume", migrationId);
                return false;
            }

            if (context.Status != "Paused")
            {
                _logger.LogWarning("Migration {MigrationId} is not in paused state, current status: {Status}", 
                    migrationId, context.Status);
                return false;
            }

            context.Status = "Running";
            context.LastUpdateTime = DateTime.UtcNow;
            
            var cancellationTokenSource = new CancellationTokenSource();
            _migrationCancellationTokens[migrationId] = cancellationTokenSource;

            // Resume migration process
            _ = Task.Run(async () => await ExecuteMigrationAsync(context, cancellationTokenSource.Token));

            _logger.LogInformation("Resumed migration {MigrationId}", migrationId);
            return true;
        }

        public async Task<bool> PauseMigrationAsync(string migrationId)
        {
            if (!_activeMigrations.TryGetValue(migrationId, out var context))
            {
                return false;
            }

            if (context.Status != "Running")
            {
                return false;
            }

            context.Status = "Paused";
            context.LastUpdateTime = DateTime.UtcNow;
            
            if (_migrationCancellationTokens.TryGetValue(migrationId, out var cancellationTokenSource))
            {
                cancellationTokenSource.Cancel();
            }

            _logger.LogInformation("Paused migration {MigrationId}", migrationId);
            return true;
        }

        public async Task<bool> CancelMigrationAsync(string migrationId)
        {
            if (!_activeMigrations.TryGetValue(migrationId, out var context))
            {
                return false;
            }

            context.Status = "Cancelled";
            context.EndTime = DateTime.UtcNow;
            context.LastUpdateTime = DateTime.UtcNow;
            
            if (_migrationCancellationTokens.TryGetValue(migrationId, out var cancellationTokenSource))
            {
                cancellationTokenSource.Cancel();
                _migrationCancellationTokens.TryRemove(migrationId, out _);
            }

            _logger.LogInformation("Cancelled migration {MigrationId}", migrationId);
            return true;
        }

        public async Task<MigrationStatus?> GetMigrationStatusAsync(string migrationId)
        {
            if (!_activeMigrations.TryGetValue(migrationId, out var context))
            {
                return null;
            }

            return new MigrationStatus
            {
                MigrationId = context.MigrationId,
                Status = context.Status,
                StartTime = context.StartTime,
                EndTime = context.EndTime,
                LastUpdateTime = context.LastUpdateTime,
                TotalItems = context.TotalItems,
                ProcessedItems = context.ProcessedItems,
                SuccessfulItems = context.SuccessfulItems,
                FailedItems = context.FailedItems,
                SkippedItems = context.SkippedItems,
                ProgressPercentage = context.TotalItems > 0 ? (double)context.ProcessedItems / context.TotalItems * 100 : 0,
                EstimatedTimeRemaining = CalculateEstimatedTimeRemaining(context),
                CurrentBatch = context.CurrentBatch,
                ErrorMessage = context.ErrorMessage,
                EnableB2Sync = context.EnableB2Sync,
                IsDryRun = context.IsDryRun,
                BatchSize = context.BatchSize,
                Metadata = context.Metadata
            };
        }

        public async Task<List<MigrationStatus>> GetAllMigrationStatusesAsync()
        {
            var statuses = new List<MigrationStatus>();
            
            foreach (var context in _activeMigrations.Values)
            {
                var status = await GetMigrationStatusAsync(context.MigrationId);
                if (status != null)
                {
                    statuses.Add(status);
                }
            }

            return statuses;
        }

        public async Task<MigrationResult?> GetMigrationResultAsync(string migrationId)
        {
            if (!_activeMigrations.TryGetValue(migrationId, out var context))
            {
                return null;
            }

            if (context.Status != "Completed" && context.Status != "Failed")
            {
                return null;
            }

            return new MigrationResult
            {
                MigrationId = context.MigrationId,
                Status = context.Status,
                StartTime = context.StartTime,
                EndTime = context.EndTime ?? DateTime.UtcNow,
                TotalItems = context.TotalItems,
                ProcessedItems = context.ProcessedItems,
                SuccessfulItems = context.SuccessfulItems,
                FailedItems = context.FailedItems,
                SkippedItems = context.SkippedItems,
                TotalProcessingTimeMs = context.TotalProcessingTimeMs,
                AverageProcessingTimeMs = context.ProcessedItems > 0 ? context.TotalProcessingTimeMs / context.ProcessedItems : 0,
                SuccessRate = context.ProcessedItems > 0 ? (double)context.SuccessfulItems / context.ProcessedItems * 100 : 0,
                ErrorMessages = context.Errors.ToList(),
                Summary = new MigrationSummary
                {
                    TotalFilesProcessed = context.ProcessedItems,
                    TotalFilesSuccessful = context.SuccessfulItems,
                    TotalFilesFailed = context.FailedItems,
                    TotalFilesSkipped = context.SkippedItems,
                    TotalDataSizeBytes = context.TotalDataSizeBytes,
                    TotalB2SyncedItems = context.B2SyncedItems,
                    TotalB2SyncFailures = context.B2SyncFailures
                }
            };
        }

        public async Task<SingleItemMigrationResult> MigrateSingleItemAsync(string s3Key, bool enableB2Sync = true)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new SingleItemMigrationResult
            {
                S3Key = s3Key,
                ProcessedAt = DateTime.UtcNow
            };

            try
            {
                _logger.LogDebug("Starting migration of single item: {S3Key}", s3Key);

                // Check if already exists in database
                var existingImage = await _galleryRepository.GetByS3KeyAsync(s3Key);
                if (existingImage != null)
                {
                    result.Success = true;
                    result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
                    result.Metadata["skipped"] = "Already exists in database";
                    return result;
                }

                // Get S3 object metadata
                var s3Metadata = await _s3StorageService.GetObjectMetadataAsync(s3Key);
                if (s3Metadata == null)
                {
                    result.Success = false;
                    result.ErrorMessage = "S3 object not found";
                    result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
                    return result;
                }

                // Create gallery image entity
                var galleryImage = await CreateGalleryImageFromS3(s3Key, s3Metadata);
                
                // Save to database
                var savedImage = await _galleryRepository.AddAsync(galleryImage);
                
                // Sync to B2 if enabled
                if (enableB2Sync)
                {
                    try
                    {
                        var b2SyncResult = await SyncToB2Async(savedImage);
                        result.B2Synced = b2SyncResult.Success;
                        result.B2Key = b2SyncResult.B2Key;
                        result.B2FileId = b2SyncResult.B2FileId;
                        
                        if (b2SyncResult.Success)
                        {
                            savedImage.B2Key = b2SyncResult.B2Key;
                            savedImage.B2Bucket = _storageConfig.B2BucketName;
                            savedImage.B2Url = b2SyncResult.B2Url;
                            await _galleryRepository.UpdateAsync(savedImage);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to sync {S3Key} to B2", s3Key);
                        result.B2Synced = false;
                        result.Metadata["b2_error"] = ex.Message;
                    }
                }

                result.Success = true;
                result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
                result.Metadata["database_id"] = savedImage.Id;
                result.Metadata["file_size"] = s3Metadata.ContentLength;

                _logger.LogDebug("Successfully migrated single item: {S3Key} in {ProcessingTimeMs}ms", 
                    s3Key, result.ProcessingTimeMs);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
                
                _logger.LogError(ex, "Failed to migrate single item: {S3Key}", s3Key);
            }

            return result;
        }

        public async Task<BatchMigrationResult> MigrateBatchAsync(IEnumerable<string> s3Keys, bool enableB2Sync = true)
        {
            var batchId = Guid.NewGuid().ToString();
            var stopwatch = Stopwatch.StartNew();
            var s3KeysList = s3Keys.ToList();
            
            var result = new BatchMigrationResult
            {
                BatchId = batchId,
                TotalItems = s3KeysList.Count,
                StartTime = DateTime.UtcNow
            };

            _logger.LogInformation("Starting batch migration {BatchId} with {TotalItems} items", 
                batchId, result.TotalItems);

            foreach (var s3Key in s3KeysList)
            {
                try
                {
                    var itemResult = await MigrateSingleItemAsync(s3Key, enableB2Sync);
                    result.ItemResults.Add(itemResult);
                    
                    if (itemResult.Success)
                    {
                        result.SuccessfulItems++;
                    }
                    else
                    {
                        result.FailedItems++;
                        result.Errors.Add($"{s3Key}: {itemResult.ErrorMessage}");
                    }
                }
                catch (Exception ex)
                {
                    result.FailedItems++;
                    result.Errors.Add($"{s3Key}: {ex.Message}");
                    _logger.LogError(ex, "Failed to migrate item {S3Key} in batch {BatchId}", s3Key, batchId);
                }
            }

            result.EndTime = DateTime.UtcNow;
            result.TotalProcessingTimeMs = stopwatch.ElapsedMilliseconds;
            result.SuccessRate = result.TotalItems > 0 ? (double)result.SuccessfulItems / result.TotalItems * 100 : 0;

            _logger.LogInformation("Completed batch migration {BatchId}: {SuccessfulItems}/{TotalItems} successful ({SuccessRate:F1}%)", 
                batchId, result.SuccessfulItems, result.TotalItems, result.SuccessRate);

            return result;
        }

        public async Task<ValidationResult> ValidateMigrationAsync(string migrationId)
        {
            if (!_activeMigrations.TryGetValue(migrationId, out var context))
            {
                throw new ArgumentException($"Migration {migrationId} not found");
            }

            var stopwatch = Stopwatch.StartNew();
            var result = new ValidationResult
            {
                MigrationId = migrationId,
                ValidationTime = DateTime.UtcNow
            };

            _logger.LogInformation("Starting validation for migration {MigrationId}", migrationId);

            try
            {
                // Get all database records for this migration
                var dbImages = await _galleryRepository.GetAllAsync(1, int.MaxValue);
                result.TotalChecked = dbImages.Items.Count;

                foreach (var dbImage in dbImages.Items)
                {
                    var validationErrors = new List<string>();

                    // Validate S3 key exists
                    if (!string.IsNullOrEmpty(dbImage.S3Key))
                    {
                        var s3Exists = await _s3StorageService.ObjectExistsAsync(dbImage.S3Key);
                        if (!s3Exists)
                        {
                            validationErrors.Add("S3 object not found");
                        }
                    }

                    // Validate B2 key exists if B2 sync was enabled
                    if (context.EnableB2Sync && !string.IsNullOrEmpty(dbImage.B2Key))
                    {
                        // Note: B2 validation would require B2 client implementation
                        // For now, we'll validate that B2 fields are populated
                        if (string.IsNullOrEmpty(dbImage.B2Bucket) || string.IsNullOrEmpty(dbImage.B2Url))
                        {
                            validationErrors.Add("B2 metadata incomplete");
                        }
                    }

                    // Validate required fields
                    if (string.IsNullOrEmpty(dbImage.Filename) || string.IsNullOrEmpty(dbImage.CatId))
                    {
                        validationErrors.Add("Required fields missing");
                    }

                    if (validationErrors.Any())
                    {
                        result.InvalidItems++;
                        result.Errors.Add(new ValidationError
                        {
                            ItemId = dbImage.Id.ToString(),
                            ErrorType = "ValidationFailure",
                            ErrorMessage = string.Join(", ", validationErrors),
                            S3Key = dbImage.S3Key ?? "",
                            B2Key = dbImage.B2Key,
                            Details = new Dictionary<string, object>
                            {
                                ["filename"] = dbImage.Filename ?? "",
                                ["catId"] = dbImage.CatId ?? ""
                            }
                        });
                    }
                    else
                    {
                        result.ValidItems++;
                    }
                }

                result.IsValid = result.InvalidItems == 0;
                result.ValidationTimeMs = stopwatch.ElapsedMilliseconds;
                result.ValidationStats = new Dictionary<string, int>
                {
                    ["total_checked"] = result.TotalChecked,
                    ["valid_items"] = result.ValidItems,
                    ["invalid_items"] = result.InvalidItems,
                    ["validation_time_ms"] = (int)result.ValidationTimeMs
                };

                _logger.LogInformation("Completed validation for migration {MigrationId}: {ValidItems}/{TotalChecked} valid", 
                    migrationId, result.ValidItems, result.TotalChecked);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to validate migration {MigrationId}", migrationId);
                result.IsValid = false;
                result.ValidationTimeMs = stopwatch.ElapsedMilliseconds;
                result.Errors.Add(new ValidationError
                {
                    ItemId = "validation_process",
                    ErrorType = "SystemError",
                    ErrorMessage = ex.Message,
                    S3Key = "",
                    Details = new Dictionary<string, object> { ["exception"] = ex.ToString() }
                });
            }

            return result;
        }

        public async Task<MigrationStatistics> GetMigrationStatisticsAsync()
        {
            var stats = new MigrationStatistics
            {
                TotalMigrations = _activeMigrations.Count,
                MigrationsByStatus = new Dictionary<string, int>(),
                ErrorsByType = new Dictionary<string, int>()
            };

            foreach (var context in _activeMigrations.Values)
            {
                stats.MigrationsByStatus[context.Status] = stats.MigrationsByStatus.GetValueOrDefault(context.Status, 0) + 1;
                
                switch (context.Status)
                {
                    case "Completed":
                        stats.CompletedMigrations++;
                        break;
                    case "Failed":
                        stats.FailedMigrations++;
                        break;
                    case "Running":
                        stats.RunningMigrations++;
                        break;
                    case "Paused":
                        stats.PausedMigrations++;
                        break;
                }

                stats.TotalItemsMigrated += context.SuccessfulItems;
                stats.TotalItemsFailed += context.FailedItems;
                
                if (context.EndTime.HasValue)
                {
                    stats.LastMigrationTime = context.EndTime.Value > stats.LastMigrationTime ? context.EndTime.Value : stats.LastMigrationTime;
                }
            }

            if (stats.CompletedMigrations > 0)
            {
                stats.AverageSuccessRate = _activeMigrations.Values
                    .Where(c => c.Status == "Completed")
                    .Average(c => c.ProcessedItems > 0 ? (double)c.SuccessfulItems / c.ProcessedItems * 100 : 0);
                
                stats.AverageProcessingTime = TimeSpan.FromMilliseconds(_activeMigrations.Values
                    .Where(c => c.Status == "Completed")
                    .Average(c => c.TotalProcessingTimeMs));
            }

            return stats;
        }

        public async Task<int> CleanupOldMigrationsAsync(int daysOld = 30)
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-daysOld);
            var cleanupCount = 0;

            var migrationsToRemove = _activeMigrations.Values
                .Where(c => c.EndTime.HasValue && c.EndTime.Value < cutoffDate)
                .ToList();

            foreach (var migration in migrationsToRemove)
            {
                _activeMigrations.TryRemove(migration.MigrationId, out _);
                _migrationCancellationTokens.TryRemove(migration.MigrationId, out _);
                cleanupCount++;
            }

            _logger.LogInformation("Cleaned up {CleanupCount} old migrations", cleanupCount);
            return cleanupCount;
        }

        public async Task<MigrationEstimation> EstimateMigrationAsync(int batchSize = 100, bool enableB2Sync = true)
        {
            var s3Objects = await _s3StorageService.ListObjectsAsync();
            var totalItems = s3Objects.Count;
            var totalSizeBytes = s3Objects.Sum(o => o.Size);
            
            // Estimate based on typical processing times
            var itemsPerSecond = 10; // Conservative estimate
            var estimatedSeconds = totalItems / itemsPerSecond;
            
            var estimation = new MigrationEstimation
            {
                TotalItems = totalItems,
                EstimatedDuration = TimeSpan.FromSeconds(estimatedSeconds),
                EstimatedDataSizeBytes = totalSizeBytes,
                EstimatedBatches = (int)Math.Ceiling((double)totalItems / batchSize),
                EstimatedB2CostUsd = enableB2Sync ? CalculateB2Cost(totalSizeBytes) : 0,
                EstimatedBandwidthGb = (double)totalSizeBytes / (1024 * 1024 * 1024),
                Recommendations = new Dictionary<string, object>
                {
                    ["optimal_batch_size"] = Math.Min(100, Math.Max(10, totalItems / 100)),
                    ["estimated_duration_hours"] = estimatedSeconds / 3600.0,
                    ["recommended_time"] = "off-peak hours"
                }
            };

            return estimation;
        }

        public async Task<MigrationProgress?> GetMigrationProgressAsync(string migrationId)
        {
            if (!_activeMigrations.TryGetValue(migrationId, out var context))
            {
                return null;
            }

            var elapsed = DateTime.UtcNow - context.StartTime;
            var itemsPerSecond = elapsed.TotalSeconds > 0 ? context.ProcessedItems / elapsed.TotalSeconds : 0;
            var bytesPerSecond = elapsed.TotalSeconds > 0 ? context.TotalDataSizeBytes / elapsed.TotalSeconds : 0;

            return new MigrationProgress
            {
                MigrationId = migrationId,
                Status = context.Status,
                ProgressPercentage = context.TotalItems > 0 ? (double)context.ProcessedItems / context.TotalItems * 100 : 0,
                CurrentBatch = context.CurrentBatchNumber,
                TotalBatches = (int)Math.Ceiling((double)context.TotalItems / context.BatchSize),
                Elapsed = elapsed,
                EstimatedTimeRemaining = CalculateEstimatedTimeRemaining(context),
                ItemsPerSecond = itemsPerSecond,
                BytesPerSecond = (long)bytesPerSecond,
                CurrentActivity = context.CurrentActivity,
                Metrics = new Dictionary<string, object>
                {
                    ["successful_items"] = context.SuccessfulItems,
                    ["failed_items"] = context.FailedItems,
                    ["b2_synced_items"] = context.B2SyncedItems,
                    ["average_processing_time_ms"] = context.ProcessedItems > 0 ? context.TotalProcessingTimeMs / context.ProcessedItems : 0
                }
            };
        }

        public async Task<RollbackResult> RollbackMigrationAsync(string migrationId)
        {
            if (!_activeMigrations.TryGetValue(migrationId, out var context))
            {
                throw new ArgumentException($"Migration {migrationId} not found");
            }

            var stopwatch = Stopwatch.StartNew();
            var result = new RollbackResult
            {
                MigrationId = migrationId,
                RollbackTime = DateTime.UtcNow
            };

            _logger.LogInformation("Starting rollback for migration {MigrationId}", migrationId);

            try
            {
                // Get all database records created during this migration
                var dbImages = await _galleryRepository.GetAllAsync(1, int.MaxValue);
                
                foreach (var dbImage in dbImages.Items)
                {
                    try
                    {
                        // Remove from database
                        await _galleryRepository.DeleteAsync(dbImage.Id);
                        result.ItemsRolledBack++;
                    }
                    catch (Exception ex)
                    {
                        result.ItemsFailed++;
                        result.Errors.Add($"Failed to rollback {dbImage.Filename}: {ex.Message}");
                        _logger.LogError(ex, "Failed to rollback database record {ImageId}", dbImage.Id);
                    }
                }

                result.Success = result.ItemsFailed == 0;
                result.RollbackTimeMs = stopwatch.ElapsedMilliseconds;

                _logger.LogInformation("Completed rollback for migration {MigrationId}: {ItemsRolledBack} items rolled back", 
                    migrationId, result.ItemsRolledBack);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.RollbackTimeMs = stopwatch.ElapsedMilliseconds;
                _logger.LogError(ex, "Failed to rollback migration {MigrationId}", migrationId);
            }

            return result;
        }

        public async Task<B2SyncResult> SyncToB2Async(int batchSize = 100)
        {
            var syncId = Guid.NewGuid().ToString();
            var stopwatch = Stopwatch.StartNew();
            
            var result = new B2SyncResult
            {
                SyncId = syncId,
                StartTime = DateTime.UtcNow
            };

            _logger.LogInformation("Starting B2 sync {SyncId} with batch size {BatchSize}", syncId, batchSize);

            try
            {
                // Get all database records that need B2 sync
                var imagesToSync = await _galleryRepository.GetImagesNeedingB2SyncAsync(1, int.MaxValue);
                result.TotalItems = imagesToSync.TotalCount;

                foreach (var image in imagesToSync.Items)
                {
                    try
                    {
                        var b2SyncResult = await SyncToB2Async(image);
                        if (b2SyncResult.Success)
                        {
                            result.SyncedItems++;
                            result.TotalSizeBytes += image.FileSize;
                        }
                        else
                        {
                            result.FailedItems++;
                            result.Errors.Add($"{image.Filename}: {b2SyncResult.ErrorMessage}");
                        }
                    }
                    catch (Exception ex)
                    {
                        result.FailedItems++;
                        result.Errors.Add($"{image.Filename}: {ex.Message}");
                        _logger.LogError(ex, "Failed to sync {ImageId} to B2", image.Id);
                    }
                }

                result.EndTime = DateTime.UtcNow;
                result.CostUsd = CalculateB2Cost(result.TotalSizeBytes);

                _logger.LogInformation("Completed B2 sync {SyncId}: {SyncedItems}/{TotalItems} synced", 
                    syncId, result.SyncedItems, result.TotalItems);
            }
            catch (Exception ex)
            {
                result.Errors.Add($"B2 sync failed: {ex.Message}");
                _logger.LogError(ex, "Failed to complete B2 sync {SyncId}", syncId);
            }

            return result;
        }

        public async Task<B2SyncVerification> VerifyB2SyncAsync()
        {
            var result = new B2SyncVerification
            {
                VerificationTime = DateTime.UtcNow
            };

            _logger.LogInformation("Starting B2 sync verification");

            try
            {
                var allImages = await _galleryRepository.GetAllAsync(1, int.MaxValue);
                result.TotalItems = allImages.TotalCount;

                foreach (var image in allImages.Items)
                {
                    if (!string.IsNullOrEmpty(image.B2Key))
                    {
                        // Note: B2 verification would require B2 client implementation
                        // For now, we'll verify that B2 fields are populated
                        if (!string.IsNullOrEmpty(image.B2Bucket) && !string.IsNullOrEmpty(image.B2Url))
                        {
                            result.VerifiedItems++;
                        }
                        else
                        {
                            result.CorruptedItems++;
                            result.CorruptedFiles.Add(image.Filename);
                        }
                    }
                    else
                    {
                        result.MissingItems++;
                        result.MissingFiles.Add(image.Filename);
                    }
                }

                result.IsValid = result.CorruptedItems == 0;

                _logger.LogInformation("Completed B2 sync verification: {VerifiedItems}/{TotalItems} verified", 
                    result.VerifiedItems, result.TotalItems);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to verify B2 sync");
                result.IsValid = false;
            }

            return result;
        }

        public async Task<MigrationLogs> GetMigrationLogsAsync(string migrationId, string logLevel = "Info", int pageSize = 100, int page = 1)
        {
            // Note: This would typically integrate with a logging framework like Serilog
            // For now, we'll return mock data structure
            return new MigrationLogs
            {
                MigrationId = migrationId,
                TotalLogs = 0,
                Page = page,
                PageSize = pageSize,
                Entries = new List<MigrationLogEntry>()
            };
        }

        // Private helper methods

        private async Task ExecuteMigrationAsync(MigrationContext context, CancellationToken cancellationToken)
        {
            try
            {
                var batches = context.S3Objects
                    .Select((obj, index) => new { obj, index })
                    .GroupBy(x => x.index / context.BatchSize)
                    .Select(g => g.Select(x => x.obj).ToList())
                    .ToList();

                context.CurrentActivity = "Processing batches";
                
                for (int i = 0; i < batches.Count; i++)
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        context.Status = "Cancelled";
                        break;
                    }

                    context.CurrentBatchNumber = i + 1;
                    context.CurrentBatch = $"Batch {i + 1}/{batches.Count}";
                    context.CurrentActivity = $"Processing batch {i + 1} of {batches.Count}";

                    var batch = batches[i];
                    var batchResult = await MigrateBatchAsync(batch.Select(o => o.Key), context.EnableB2Sync);

                    context.ProcessedItems += batchResult.TotalItems;
                    context.SuccessfulItems += batchResult.SuccessfulItems;
                    context.FailedItems += batchResult.FailedItems;
                    context.TotalProcessingTimeMs += batchResult.TotalProcessingTimeMs;
                    context.Errors.AddRange(batchResult.Errors);

                    context.LastUpdateTime = DateTime.UtcNow;
                    
                    // Add small delay to prevent overwhelming the system
                    await Task.Delay(100, cancellationToken);
                }

                if (context.Status != "Cancelled")
                {
                    context.Status = context.FailedItems == 0 ? "Completed" : "Failed";
                }
                
                context.EndTime = DateTime.UtcNow;
                context.CurrentActivity = "Migration complete";
                
                _logger.LogInformation("Migration {MigrationId} completed with status {Status}", 
                    context.MigrationId, context.Status);
            }
            catch (Exception ex)
            {
                context.Status = "Failed";
                context.EndTime = DateTime.UtcNow;
                context.ErrorMessage = ex.Message;
                context.Errors.Add(ex.Message);
                
                _logger.LogError(ex, "Migration {MigrationId} failed", context.MigrationId);
            }
        }

        private async Task<CatGalleryImage> CreateGalleryImageFromS3(string s3Key, S3ObjectMetadata metadata)
        {
            var filename = System.IO.Path.GetFileName(s3Key);
            var catId = ExtractCatIdFromFilename(filename);
            
            return new CatGalleryImage
            {
                Filename = filename,
                CatId = catId,
                S3Key = s3Key,
                S3Bucket = _storageConfig.S3BucketName,
                S3Url = $"https://{_storageConfig.S3BucketName}.s3.amazonaws.com/{s3Key}",
                StorageProvider = "S3",
                FileSize = metadata.ContentLength,
                MimeType = metadata.ContentType ?? "image/jpeg",
                Format = GetFormatFromMimeType(metadata.ContentType),
                Width = 0, // Would need image processing to determine
                Height = 0, // Would need image processing to determine
                IsActive = true,
                IsPublic = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
        }

        private async Task<B2SyncItemResult> SyncToB2Async(CatGalleryImage image)
        {
            // Note: B2 sync implementation would require B2 client
            // For now, we'll simulate the sync process
            return new B2SyncItemResult
            {
                Success = true,
                B2Key = $"b2/{image.Filename}",
                B2FileId = Guid.NewGuid().ToString(),
                B2Url = $"https://b2.example.com/{image.Filename}"
            };
        }

        private string ExtractCatIdFromFilename(string filename)
        {
            // Simple extraction logic - would be more sophisticated in reality
            var nameWithoutExtension = System.IO.Path.GetFileNameWithoutExtension(filename);
            var parts = nameWithoutExtension.Split('_', '-');
            return parts.Length > 0 ? parts[0].ToLower() : "unknown";
        }

        private string GetFormatFromMimeType(string? mimeType)
        {
            return mimeType switch
            {
                "image/jpeg" => "JPEG",
                "image/jpg" => "JPEG",
                "image/png" => "PNG",
                "image/gif" => "GIF",
                "image/webp" => "WEBP",
                _ => "JPEG"
            };
        }

        private TimeSpan? CalculateEstimatedTimeRemaining(MigrationContext context)
        {
            if (context.ProcessedItems == 0 || context.Status != "Running")
            {
                return null;
            }

            var elapsed = DateTime.UtcNow - context.StartTime;
            var itemsPerSecond = context.ProcessedItems / elapsed.TotalSeconds;
            var remainingItems = context.TotalItems - context.ProcessedItems;
            var estimatedSeconds = remainingItems / itemsPerSecond;

            return TimeSpan.FromSeconds(estimatedSeconds);
        }

        private double CalculateB2Cost(long totalSizeBytes)
        {
            // Simple B2 cost calculation - would be more accurate in reality
            var sizeInGb = (double)totalSizeBytes / (1024 * 1024 * 1024);
            var costPerGb = 0.005; // Approximate B2 cost per GB
            return sizeInGb * costPerGb;
        }
    }

    // Internal helper classes

    internal class MigrationContext
    {
        public string MigrationId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public DateTime? LastUpdateTime { get; set; }
        public int TotalItems { get; set; }
        public int ProcessedItems { get; set; }
        public int SuccessfulItems { get; set; }
        public int FailedItems { get; set; }
        public int SkippedItems { get; set; }
        public int BatchSize { get; set; }
        public bool EnableB2Sync { get; set; }
        public bool IsDryRun { get; set; }
        public string? CurrentBatch { get; set; }
        public int CurrentBatchNumber { get; set; }
        public string? CurrentActivity { get; set; }
        public string? ErrorMessage { get; set; }
        public List<string> Errors { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
        public List<S3ObjectInfo> S3Objects { get; set; } = new();
        public long TotalProcessingTimeMs { get; set; }
        public long TotalDataSizeBytes { get; set; }
        public int B2SyncedItems { get; set; }
        public int B2SyncFailures { get; set; }
    }

    internal class B2SyncItemResult
    {
        public bool Success { get; set; }
        public string? B2Key { get; set; }
        public string? B2FileId { get; set; }
        public string? B2Url { get; set; }
        public string? ErrorMessage { get; set; }
    }

}