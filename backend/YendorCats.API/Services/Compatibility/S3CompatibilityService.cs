using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using YendorCats.API.Data.Repositories;
using YendorCats.API.Models;
using YendorCats.API.Services.Migration;

namespace YendorCats.API.Services.Compatibility
{
    /// <summary>
    /// Backward compatibility service for existing S3 operations
    /// Provides seamless transition from S3-only to hybrid storage architecture
    /// Maintains existing API contracts while supporting new dual storage features
    /// </summary>
    public class S3CompatibilityService : IS3CompatibilityService
    {
        private readonly IGalleryRepository _galleryRepository;
        private readonly IS3StorageService _s3StorageService;
        private readonly IS3ToDbMigrationService _migrationService;
        private readonly ILogger<S3CompatibilityService> _logger;

        public S3CompatibilityService(
            IGalleryRepository galleryRepository,
            IS3StorageService s3StorageService,
            IS3ToDbMigrationService migrationService,
            ILogger<S3CompatibilityService> logger)
        {
            _galleryRepository = galleryRepository;
            _s3StorageService = s3StorageService;
            _migrationService = migrationService;
            _logger = logger;
        }

        /// <summary>
        /// Get all images with backward compatibility for S3-only operations
        /// Transparently uses database if available, falls back to S3 scanning
        /// </summary>
        /// <returns>List of gallery images</returns>
        public async Task<List<LegacyGalleryImage>> GetAllImagesAsync()
        {
            try
            {
                _logger.LogDebug("Getting all images with backward compatibility");

                // First, try to get from database (new hybrid approach)
                var dbImages = await _galleryRepository.GetAllAsync(1, int.MaxValue);
                
                if (dbImages.TotalCount > 0)
                {
                    _logger.LogDebug("Retrieved {Count} images from database", dbImages.TotalCount);
                    return dbImages.Items.Select(ConvertToLegacyFormat).ToList();
                }

                // Fall back to S3 scanning (legacy approach)
                _logger.LogDebug("No database images found, falling back to S3 scanning");
                var s3ObjectKeys = await _s3StorageService.ListObjectsAsync();
                var legacyImages = new List<LegacyGalleryImage>();

                foreach (var s3ObjectKey in s3ObjectKeys)
                {
                    var s3ObjectInfo = CreateS3ObjectInfoFromKey(s3ObjectKey);
                    var legacyImage = await ConvertS3ObjectToLegacyFormat(s3ObjectInfo);
                    legacyImages.Add(legacyImage);
                }

                _logger.LogDebug("Retrieved {Count} images from S3", legacyImages.Count);
                return legacyImages;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get all images with backward compatibility");
                throw;
            }
        }

        /// <summary>
        /// Get images by category with backward compatibility
        /// </summary>
        /// <param name="category">Image category</param>
        /// <returns>List of gallery images in category</returns>
        public async Task<List<LegacyGalleryImage>> GetImagesByCategoryAsync(string category)
        {
            try
            {
                _logger.LogDebug("Getting images by category: {Category}", category);

                // Try database first
                var dbImages = await _galleryRepository.GetByCatIdAsync(category, 1, int.MaxValue);
                
                if (dbImages.TotalCount > 0)
                {
                    _logger.LogDebug("Retrieved {Count} images from database for category {Category}", 
                        dbImages.TotalCount, category);
                    return dbImages.Items.Select(ConvertToLegacyFormat).ToList();
                }

                // Fall back to S3 scanning with category filtering
                _logger.LogDebug("No database images found for category, falling back to S3 scanning");
                var s3ObjectKeys = await _s3StorageService.ListObjectsAsync();
                var categoryKeys = s3ObjectKeys.Where(key =>
                    key.Contains(category, StringComparison.OrdinalIgnoreCase)).ToList();

                var legacyImages = new List<LegacyGalleryImage>();
                foreach (var s3ObjectKey in categoryKeys)
                {
                    var s3ObjectInfo = CreateS3ObjectInfoFromKey(s3ObjectKey);
                    var legacyImage = await ConvertS3ObjectToLegacyFormat(s3ObjectInfo);
                    legacyImages.Add(legacyImage);
                }

                _logger.LogDebug("Retrieved {Count} images from S3 for category {Category}", 
                    legacyImages.Count, category);
                return legacyImages;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get images by category: {Category}", category);
                throw;
            }
        }

        /// <summary>
        /// Get image by filename with backward compatibility
        /// </summary>
        /// <param name="filename">Image filename</param>
        /// <returns>Gallery image or null if not found</returns>
        public async Task<LegacyGalleryImage?> GetImageByFilenameAsync(string filename)
        {
            try
            {
                _logger.LogDebug("Getting image by filename: {Filename}", filename);

                // Try database first
                var dbImage = await _galleryRepository.GetByFilenameAsync(filename);
                if (dbImage != null)
                {
                    _logger.LogDebug("Retrieved image from database: {Filename}", filename);
                    return ConvertToLegacyFormat(dbImage);
                }

                // Fall back to S3 lookup
                _logger.LogDebug("Image not found in database, checking S3: {Filename}", filename);
                var s3ObjectKeys = await _s3StorageService.ListObjectsAsync();
                var s3ObjectKey = s3ObjectKeys.FirstOrDefault(key =>
                    key.EndsWith(filename, StringComparison.OrdinalIgnoreCase));

                if (s3ObjectKey != null)
                {
                    _logger.LogDebug("Found image in S3: {Filename}", filename);
                    var s3ObjectInfo = CreateS3ObjectInfoFromKey(s3ObjectKey);
                    return await ConvertS3ObjectToLegacyFormat(s3ObjectInfo);
                }

                _logger.LogDebug("Image not found: {Filename}", filename);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get image by filename: {Filename}", filename);
                throw;
            }
        }

        /// <summary>
        /// Get image metadata with backward compatibility
        /// </summary>
        /// <param name="filename">Image filename</param>
        /// <returns>Image metadata or null if not found</returns>
        public async Task<LegacyImageMetadata?> GetImageMetadataAsync(string filename)
        {
            try
            {
                _logger.LogDebug("Getting image metadata: {Filename}", filename);

                // Try database first
                var dbImage = await _galleryRepository.GetByFilenameAsync(filename);
                if (dbImage != null)
                {
                    _logger.LogDebug("Retrieved metadata from database: {Filename}", filename);
                    return ConvertToLegacyMetadata(dbImage);
                }

                // Fall back to S3 metadata
                _logger.LogDebug("Metadata not found in database, checking S3: {Filename}", filename);
                var s3ObjectKeys = await _s3StorageService.ListObjectsAsync();
                var s3ObjectKey = s3ObjectKeys.FirstOrDefault(key =>
                    key.EndsWith(filename, StringComparison.OrdinalIgnoreCase));

                if (s3ObjectKey != null)
                {
                    var s3Metadata = await _s3StorageService.GetObjectMetadataAsync(s3ObjectKey);
                    if (s3Metadata != null)
                    {
                        _logger.LogDebug("Retrieved metadata from S3: {Filename}", filename);
                        var s3ObjectInfo = CreateS3ObjectInfoFromKey(s3ObjectKey);
                        return ConvertS3MetadataToLegacyFormat(s3ObjectInfo, s3Metadata);
                    }
                }

                _logger.LogDebug("Metadata not found: {Filename}", filename);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get image metadata: {Filename}", filename);
                throw;
            }
        }

        /// <summary>
        /// Check if migration is needed for the gallery
        /// </summary>
        /// <returns>True if migration is needed, false otherwise</returns>
        public async Task<bool> IsMigrationNeededAsync()
        {
            try
            {
                // Check if database has any images
                var dbCount = await _galleryRepository.GetTotalCountAsync(activeOnly: false, publicOnly: false);
                
                // Check if S3 has any images
                var s3ObjectKeys = await _s3StorageService.ListObjectsAsync();
                var s3Count = s3ObjectKeys.Count;

                // Migration is needed if S3 has images but database doesn't, or if counts don't match
                var migrationNeeded = s3Count > 0 && (dbCount == 0 || dbCount < s3Count);
                
                _logger.LogInformation("Migration check: S3 has {S3Count} images, DB has {DbCount} images, migration needed: {MigrationNeeded}", 
                    s3Count, dbCount, migrationNeeded);

                return migrationNeeded;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to check if migration is needed");
                return false;
            }
        }

        /// <summary>
        /// Start automatic migration if needed
        /// </summary>
        /// <returns>Migration ID if started, null if not needed</returns>
        public async Task<string?> StartMigrationIfNeededAsync()
        {
            try
            {
                if (await IsMigrationNeededAsync())
                {
                    _logger.LogInformation("Starting automatic migration");
                    var migrationId = await _migrationService.StartMigrationAsync(
                        batchSize: 50, 
                        enableB2Sync: true, 
                        dryRun: false);
                    
                    _logger.LogInformation("Started migration with ID: {MigrationId}", migrationId);
                    return migrationId;
                }

                _logger.LogDebug("No migration needed");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to start migration");
                throw;
            }
        }

        /// <summary>
        /// Get migration progress for backward compatibility monitoring
        /// </summary>
        /// <returns>Migration progress summary</returns>
        public async Task<LegacyMigrationProgress?> GetMigrationProgressAsync()
        {
            try
            {
                // Get all active migrations
                var migrations = await _migrationService.GetAllMigrationStatusesAsync();
                var activeMigration = migrations.FirstOrDefault(m => m.Status == "Running");

                if (activeMigration == null)
                {
                    // Check for recently completed migrations
                    var recentMigration = migrations
                        .Where(m => m.Status == "Completed" || m.Status == "Failed")
                        .OrderByDescending(m => m.EndTime)
                        .FirstOrDefault();

                    if (recentMigration != null)
                    {
                        return new LegacyMigrationProgress
                        {
                            IsRunning = false,
                            Status = recentMigration.Status,
                            ProgressPercentage = 100,
                            ProcessedItems = recentMigration.ProcessedItems,
                            TotalItems = recentMigration.TotalItems,
                            SuccessfulItems = recentMigration.SuccessfulItems,
                            FailedItems = recentMigration.FailedItems,
                            StartTime = recentMigration.StartTime,
                            EndTime = recentMigration.EndTime,
                            EstimatedTimeRemaining = null
                        };
                    }

                    return null;
                }

                return new LegacyMigrationProgress
                {
                    IsRunning = true,
                    Status = activeMigration.Status,
                    ProgressPercentage = activeMigration.ProgressPercentage,
                    ProcessedItems = activeMigration.ProcessedItems,
                    TotalItems = activeMigration.TotalItems,
                    SuccessfulItems = activeMigration.SuccessfulItems,
                    FailedItems = activeMigration.FailedItems,
                    StartTime = activeMigration.StartTime,
                    EndTime = activeMigration.EndTime,
                    EstimatedTimeRemaining = activeMigration.EstimatedTimeRemaining
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get migration progress");
                return null;
            }
        }

        /// <summary>
        /// Get system status for backward compatibility
        /// </summary>
        /// <returns>System status information</returns>
        public async Task<LegacySystemStatus> GetSystemStatusAsync()
        {
            try
            {
                var status = new LegacySystemStatus
                {
                    DatabaseConnected = true, // Assume true for now
                    S3Connected = true,       // Assume true for now
                    MigrationActive = false,
                    LastChecked = DateTime.UtcNow
                };

                // Check if migration is active
                var migrations = await _migrationService.GetAllMigrationStatusesAsync();
                status.MigrationActive = migrations.Any(m => m.Status == "Running");

                // Get basic statistics
                var dbCount = await _galleryRepository.GetTotalCountAsync(activeOnly: false, publicOnly: false);
                var s3ObjectKeys = await _s3StorageService.ListObjectsAsync();
                
                status.DatabaseImageCount = dbCount;
                status.S3ImageCount = s3ObjectKeys.Count;
                status.SyncStatus = dbCount == s3ObjectKeys.Count ? "Synchronized" : "Out of sync";

                return status;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get system status");
                return new LegacySystemStatus
                {
                    DatabaseConnected = false,
                    S3Connected = false,
                    MigrationActive = false,
                    LastChecked = DateTime.UtcNow,
                    ErrorMessage = ex.Message
                };
            }
        }

        // Private helper methods

        private LegacyGalleryImage ConvertToLegacyFormat(CatGalleryImage dbImage)
        {
            return new LegacyGalleryImage
            {
                Id = dbImage.Id,
                Filename = dbImage.Filename,
                ImageUrl = dbImage.S3Url ?? $"https://s3.amazonaws.com/bucket/{dbImage.S3Key}",
                CatName = ExtractCatNameFromCatId(dbImage.CatId),
                Category = dbImage.CatId,
                Description = dbImage.Description ?? "",
                FileSize = dbImage.FileSize,
                Width = dbImage.Width,
                Height = dbImage.Height,
                CreatedAt = dbImage.CreatedAt,
                UpdatedAt = dbImage.UpdatedAt,
                Tags = dbImage.Tags ?? "",
                IsActive = dbImage.IsActive,
                IsFeatured = dbImage.IsFeatured,
                ViewCount = dbImage.ViewCount,
                LikeCount = dbImage.LikeCount
            };
        }

        private async Task<LegacyGalleryImage> ConvertS3ObjectToLegacyFormat(S3ObjectInfo s3Object)
        {
            var metadata = await _s3StorageService.GetObjectMetadataAsync(s3Object.Key);
            var filename = System.IO.Path.GetFileName(s3Object.Key);
            
            return new LegacyGalleryImage
            {
                Id = 0, // S3 objects don't have database IDs
                Filename = filename,
                ImageUrl = $"https://s3.amazonaws.com/bucket/{s3Object.Key}",
                CatName = ExtractCatNameFromFilename(filename),
                Category = ExtractCategoryFromS3Key(s3Object.Key),
                Description = "",
                FileSize = s3Object.Size,
                Width = 0, // Would need image processing to determine
                Height = 0, // Would need image processing to determine
                CreatedAt = s3Object.LastModified,
                UpdatedAt = s3Object.LastModified,
                Tags = "",
                IsActive = true,
                IsFeatured = false,
                ViewCount = 0,
                LikeCount = 0
            };
        }

        private LegacyImageMetadata ConvertToLegacyMetadata(CatGalleryImage dbImage)
        {
            return new LegacyImageMetadata
            {
                Filename = dbImage.Filename,
                FileSize = dbImage.FileSize,
                Width = dbImage.Width,
                Height = dbImage.Height,
                Format = dbImage.Format,
                MimeType = dbImage.MimeType,
                CreatedAt = dbImage.CreatedAt,
                UpdatedAt = dbImage.UpdatedAt,
                S3Key = dbImage.S3Key ?? "",
                S3Url = dbImage.S3Url ?? "",
                ExifData = dbImage.ExifData
            };
        }

        private LegacyImageMetadata ConvertS3MetadataToLegacyFormat(S3ObjectInfo s3Object, S3ObjectMetadata s3Metadata)
        {
            return new LegacyImageMetadata
            {
                Filename = System.IO.Path.GetFileName(s3Object.Key),
                FileSize = s3Metadata.ContentLength,
                Width = 0, // Would need image processing
                Height = 0, // Would need image processing
                Format = GetFormatFromContentType(s3Metadata.ContentType),
                MimeType = s3Metadata.ContentType ?? "image/jpeg",
                CreatedAt = s3Metadata.LastModified,
                UpdatedAt = s3Metadata.LastModified,
                S3Key = s3Object.Key,
                S3Url = $"https://s3.amazonaws.com/bucket/{s3Object.Key}",
                ExifData = null
            };
        }

        private string ExtractCatNameFromCatId(string catId)
        {
            // Simple conversion from cat ID to display name
            return catId.Replace("_", " ").Replace("-", " ");
        }

        private string ExtractCatNameFromFilename(string filename)
        {
            // Extract cat name from filename
            var nameWithoutExtension = System.IO.Path.GetFileNameWithoutExtension(filename);
            var parts = nameWithoutExtension.Split('_', '-');
            return parts.Length > 0 ? parts[0].Replace("_", " ") : "Unknown";
        }

        private string ExtractCategoryFromS3Key(string s3Key)
        {
            // Extract category from S3 key structure
            var parts = s3Key.Split('/');
            return parts.Length > 1 ? parts[0] : "general";
        }

        private string GetFormatFromContentType(string? contentType)
        {
            return contentType switch
            {
                "image/jpeg" => "JPEG",
                "image/jpg" => "JPEG",
                "image/png" => "PNG",
                "image/gif" => "GIF",
                "image/webp" => "WEBP",
                _ => "JPEG"
            };
        }

        /// <summary>
        /// Create an S3ObjectInfo from an object key with minimal default data
        /// </summary>
        private S3ObjectInfo CreateS3ObjectInfoFromKey(string key)
        {
            return new S3ObjectInfo
            {
                Key = key,
                Size = 0, // Size will be determined by metadata lookup if needed
                LastModified = DateTime.UtcNow, // Default to current time
                ETag = ""
            };
        }
    }
}