using System.Collections.Concurrent;
using System.Diagnostics;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Caching.Distributed;
using YendorCats.API.Services.Gallery;
using YendorCats.API.Data.Repositories;

namespace YendorCats.API.Services.Performance
{
    /// <summary>
    /// Intelligent cache warmup service for optimal performance
    /// Proactively loads popular content into cache to minimize cold cache penalties
    /// </summary>
    public class CacheWarmupService : ICacheWarmupService
    {
        private readonly IGalleryService _galleryService;
        private readonly IGalleryRepository _galleryRepository;
        private readonly IMemoryCache _memoryCache;
        private readonly IDistributedCache _distributedCache;
        private readonly IPerformanceMetricsService _metricsService;
        private readonly ILogger<CacheWarmupService> _logger;
        
        // Warmup state management
        private static readonly ConcurrentDictionary<string, WarmupSchedule> _activeSchedules = new();
        private static readonly ConcurrentQueue<WarmupHistoryEntry> _warmupHistory = new();
        private static volatile bool _warmupInProgress = false;
        private static string? _currentOperation = null;
        private static DateTime? _lastWarmupTime = null;
        
        // Configuration
        private const int MAX_CONCURRENT_WARMUPS = 3;
        private const int MAX_HISTORY_ENTRIES = 1000;
        private const int DEFAULT_WARMUP_BATCH_SIZE = 20;
        private readonly SemaphoreSlim _warmupSemaphore;
        
        // Popular categories based on typical usage patterns
        private static readonly Dictionary<string, int> _categoryPriority = new()
        {
            { "gallery", 1 },
            { "kittens", 2 },
            { "queens", 3 },
            { "studs", 4 }
        };

        public CacheWarmupService(
            IGalleryService galleryService,
            IGalleryRepository galleryRepository,
            IMemoryCache memoryCache,
            IDistributedCache distributedCache,
            IPerformanceMetricsService metricsService,
            ILogger<CacheWarmupService> logger)
        {
            _galleryService = galleryService;
            _galleryRepository = galleryRepository;
            _memoryCache = memoryCache;
            _distributedCache = distributedCache;
            _metricsService = metricsService;
            _logger = logger;
            _warmupSemaphore = new SemaphoreSlim(MAX_CONCURRENT_WARMUPS, MAX_CONCURRENT_WARMUPS);
        }

        public async Task<WarmupResult> WarmupCategoryAsync(string category, WarmupStrategy strategy = WarmupStrategy.Popular)
        {
            var result = new WarmupResult
            {
                Strategy = strategy,
                Category = category,
                StartTime = DateTime.UtcNow
            };
            
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                await _warmupSemaphore.WaitAsync();
                _warmupInProgress = true;
                _currentOperation = $"Warming up {category} using {strategy} strategy";
                
                _logger.LogInformation("Starting cache warmup for category {Category} using {Strategy} strategy", category, strategy);
                
                // Get images to warm up based on strategy
                var imagesToWarmup = await GetImagesForWarmupAsync(category, strategy, DEFAULT_WARMUP_BATCH_SIZE);
                result.TotalItems = imagesToWarmup.Count;
                
                if (!imagesToWarmup.Any())
                {
                    result.Success = true;
                    result.EndTime = DateTime.UtcNow;
                    result.Duration = stopwatch.Elapsed;
                    return result;
                }
                
                // Warm up images in parallel batches
                var batchSize = Math.Min(5, imagesToWarmup.Count);
                var batches = imagesToWarmup.Chunk(batchSize);
                
                foreach (var batch in batches)
                {
                    var warmupTasks = batch.Select(async imageId =>
                    {
                        try
                        {
                            // Load image metadata into cache
                            var image = await _galleryService.GetImageAsync(imageId);
                            if (image != null)
                            {
                                result.WarmedUpItems++;
                                return true;
                            }
                            else
                            {
                                result.SkippedItems++;
                                return false;
                            }
                        }
                        catch (Exception ex)
                        {
                            result.FailedItems++;
                            result.Errors.Add($"Failed to warm up image {imageId}: {ex.Message}");
                            _logger.LogWarning(ex, "Failed to warm up image {ImageId}", imageId);
                            return false;
                        }
                    });
                    
                    await Task.WhenAll(warmupTasks);
                    
                    // Small delay between batches to avoid overwhelming the system
                    await Task.Delay(100);
                }
                
                // Also warm up category-level cache
                try
                {
                    await _galleryService.GetCategoryImagesAsync(category, 1, DEFAULT_WARMUP_BATCH_SIZE);
                    _logger.LogDebug("Warmed up category-level cache for {Category}", category);
                }
                catch (Exception ex)
                {
                    result.Errors.Add($"Failed to warm up category cache: {ex.Message}");
                    _logger.LogWarning(ex, "Failed to warm up category cache for {Category}", category);
                }
                
                result.Success = result.FailedItems < result.TotalItems / 2; // Success if less than 50% failed
                result.EndTime = DateTime.UtcNow;
                result.Duration = stopwatch.Elapsed;
                
                // Record metrics
                await _metricsService.RecordCacheOperationAsync("warmup", "category", result.Duration, true, category);
                
                _logger.LogInformation("Cache warmup completed for {Category}: {WarmedUp}/{Total} items in {Duration}ms", 
                    category, result.WarmedUpItems, result.TotalItems, result.Duration.TotalMilliseconds);
                
                // Add to history
                AddToHistory(new WarmupHistoryEntry
                {
                    Timestamp = result.StartTime,
                    Strategy = strategy,
                    Category = category,
                    ItemsWarmedUp = result.WarmedUpItems,
                    Duration = result.Duration,
                    Success = result.Success,
                    EffectivenessScore = result.SuccessRate
                });
                
                return result;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Errors.Add($"Warmup operation failed: {ex.Message}");
                result.EndTime = DateTime.UtcNow;
                result.Duration = stopwatch.Elapsed;
                
                _logger.LogError(ex, "Cache warmup failed for category {Category}", category);
                return result;
            }
            finally
            {
                _warmupInProgress = false;
                _currentOperation = null;
                _lastWarmupTime = DateTime.UtcNow;
                _warmupSemaphore.Release();
            }
        }

        public async Task<BatchWarmupResult> WarmupAllCategoriesAsync(WarmupStrategy strategy = WarmupStrategy.Popular)
        {
            var result = new BatchWarmupResult
            {
                StartTime = DateTime.UtcNow
            };
            
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                var categories = _categoryPriority.Keys.ToList();
                result.TotalCategories = categories.Count;
                
                _logger.LogInformation("Starting batch warmup for all categories using {Strategy} strategy", strategy);
                
                // Warm up categories in priority order
                foreach (var category in categories.OrderBy(c => _categoryPriority[c]))
                {
                    try
                    {
                        var categoryResult = await WarmupCategoryAsync(category, strategy);
                        result.CategoryResults.Add(categoryResult);
                        result.TotalItemsWarmedUp += categoryResult.WarmedUpItems;
                        
                        if (categoryResult.Success)
                        {
                            result.SuccessfulCategories++;
                        }
                        else
                        {
                            result.FailedCategories++;
                        }
                        
                        // Small delay between categories
                        await Task.Delay(500);
                    }
                    catch (Exception ex)
                    {
                        result.FailedCategories++;
                        _logger.LogError(ex, "Failed to warm up category {Category}", category);
                    }
                }
                
                result.EndTime = DateTime.UtcNow;
                result.TotalDuration = stopwatch.Elapsed;
                
                _logger.LogInformation("Batch warmup completed: {Successful}/{Total} categories, {TotalItems} items warmed up in {Duration}ms",
                    result.SuccessfulCategories, result.TotalCategories, result.TotalItemsWarmedUp, result.TotalDuration.TotalMilliseconds);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Batch warmup operation failed");
                result.EndTime = DateTime.UtcNow;
                result.TotalDuration = stopwatch.Elapsed;
                return result;
            }
        }

        public async Task<WarmupResult> WarmupSpecificImagesAsync(IEnumerable<long> imageIds)
        {
            var result = new WarmupResult
            {
                Strategy = WarmupStrategy.Custom,
                StartTime = DateTime.UtcNow
            };
            
            var stopwatch = Stopwatch.StartNew();
            var imageIdsList = imageIds.ToList();
            result.TotalItems = imageIdsList.Count;
            
            try
            {
                await _warmupSemaphore.WaitAsync();
                _warmupInProgress = true;
                _currentOperation = $"Warming up {imageIdsList.Count} specific images";
                
                var warmupTasks = imageIdsList.Select(async imageId =>
                {
                    try
                    {
                        var image = await _galleryService.GetImageAsync(imageId);
                        if (image != null)
                        {
                            Interlocked.Increment(ref result.WarmedUpItems);
                        }
                        else
                        {
                            Interlocked.Increment(ref result.SkippedItems);
                        }
                    }
                    catch (Exception ex)
                    {
                        Interlocked.Increment(ref result.FailedItems);
                        lock (result.Errors)
                        {
                            result.Errors.Add($"Image {imageId}: {ex.Message}");
                        }
                    }
                });
                
                await Task.WhenAll(warmupTasks);
                
                result.Success = result.FailedItems < result.TotalItems / 2;
                result.EndTime = DateTime.UtcNow;
                result.Duration = stopwatch.Elapsed;
                
                return result;
            }
            finally
            {
                _warmupInProgress = false;
                _currentOperation = null;
                _lastWarmupTime = DateTime.UtcNow;
                _warmupSemaphore.Release();
            }
        }

        public async Task<WarmupResult> WarmupByAccessPatternsAsync(TimeSpan lookbackPeriod)
        {
            var result = new WarmupResult
            {
                Strategy = WarmupStrategy.Predictive,
                StartTime = DateTime.UtcNow
            };

            try
            {
                // In a real implementation, this would analyze access logs
                // For now, we'll use recent uploads as a proxy
                var cutoff = DateTime.UtcNow - lookbackPeriod;
                var recentImages = await _galleryRepository.GetRecentlyAccessedImagesAsync(cutoff, DEFAULT_WARMUP_BATCH_SIZE);

                if (recentImages.Any())
                {
                    return await WarmupSpecificImagesAsync(recentImages.Select(img => img.Id));
                }
                else
                {
                    // Fallback to popular strategy
                    return await WarmupAllCategoriesAsync(WarmupStrategy.Popular);
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Errors.Add($"Access pattern warmup failed: {ex.Message}");
                result.EndTime = DateTime.UtcNow;
                _logger.LogError(ex, "Failed to warm up by access patterns");
                return result;
            }
        }

        public async Task<WarmupResult> WarmupForPeakHoursAsync()
        {
            // Warm up content that's typically accessed during peak hours
            var result = new WarmupResult
            {
                Strategy = WarmupStrategy.Predictive,
                StartTime = DateTime.UtcNow
            };

            try
            {
                // Peak hours strategy: warm up gallery and kittens (most popular)
                var galleryResult = await WarmupCategoryAsync("gallery", WarmupStrategy.Popular);
                var kittensResult = await WarmupCategoryAsync("kittens", WarmupStrategy.Recent);

                result.TotalItems = galleryResult.TotalItems + kittensResult.TotalItems;
                result.WarmedUpItems = galleryResult.WarmedUpItems + kittensResult.WarmedUpItems;
                result.SkippedItems = galleryResult.SkippedItems + kittensResult.SkippedItems;
                result.FailedItems = galleryResult.FailedItems + kittensResult.FailedItems;
                result.Errors.AddRange(galleryResult.Errors);
                result.Errors.AddRange(kittensResult.Errors);
                result.Success = galleryResult.Success && kittensResult.Success;
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime.Value - result.StartTime;

                return result;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Errors.Add($"Peak hours warmup failed: {ex.Message}");
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime.Value - result.StartTime;
                return result;
            }
        }

        public async Task ScheduleWarmupAsync(WarmupSchedule schedule)
        {
            _activeSchedules.TryAdd(schedule.Id, schedule);
            _logger.LogInformation("Scheduled warmup {ScheduleId}: {Name} with cron {Cron}",
                schedule.Id, schedule.Name, schedule.CronExpression);
        }

        public async Task CancelScheduledWarmupAsync(string scheduleId)
        {
            if (_activeSchedules.TryRemove(scheduleId, out var schedule))
            {
                _logger.LogInformation("Cancelled scheduled warmup {ScheduleId}: {Name}", scheduleId, schedule.Name);
            }
        }

        public async Task<List<WarmupSchedule>> GetActiveSchedulesAsync()
        {
            return _activeSchedules.Values.Where(s => s.IsActive).ToList();
        }

        public async Task<WarmupRecommendation> AnalyzeAndRecommendAsync()
        {
            var recommendation = new WarmupRecommendation();

            try
            {
                // Analyze recent performance metrics
                var systemMetrics = await _metricsService.GetSystemMetricsAsync(TimeSpan.FromHours(24));
                var cacheMetrics = await _metricsService.GetCacheMetricsAsync(TimeSpan.FromHours(24));

                // Recommend strategy based on cache hit rate
                if (cacheMetrics.OverallHitRate < 0.6)
                {
                    recommendation.RecommendedStrategy = WarmupStrategy.Popular;
                    recommendation.Reasons.Add("Low cache hit rate detected - recommend popular content warmup");
                    recommendation.RecommendedFrequencyHours = 2;
                }
                else if (cacheMetrics.OverallHitRate < 0.8)
                {
                    recommendation.RecommendedStrategy = WarmupStrategy.Recent;
                    recommendation.Reasons.Add("Moderate cache performance - recommend recent content warmup");
                    recommendation.RecommendedFrequencyHours = 4;
                }
                else
                {
                    recommendation.RecommendedStrategy = WarmupStrategy.Trending;
                    recommendation.Reasons.Add("Good cache performance - recommend trending content warmup");
                    recommendation.RecommendedFrequencyHours = 6;
                }

                // Priority categories based on performance
                recommendation.PriorityCategories = _categoryPriority.Keys.OrderBy(k => _categoryPriority[k]).ToList();

                // Optimal warmup time (early morning when traffic is low)
                recommendation.OptimalWarmupTime = TimeSpan.FromHours(3); // 3 AM

                recommendation.ConfidenceScore = 0.8; // High confidence in basic recommendations

                return recommendation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to analyze and recommend warmup strategy");
                recommendation.RecommendedStrategy = WarmupStrategy.Popular;
                recommendation.RecommendedFrequencyHours = 4;
                recommendation.ConfidenceScore = 0.3;
                return recommendation;
            }
        }

        public async Task<List<long>> PredictContentToWarmupAsync(int count = 50)
        {
            try
            {
                // Simple prediction based on recent activity and popularity
                var recentImages = await _galleryRepository.GetCategoryImagesAsync("gallery", 1, count / 2, "DateUploaded", true);
                var popularImages = await _galleryRepository.GetCategoryImagesAsync("gallery", 1, count / 2, "AccessCount", true);

                var predictedIds = new HashSet<long>();
                predictedIds.UnionWith(recentImages.Items.Select(img => img.Id));
                predictedIds.UnionWith(popularImages.Items.Select(img => img.Id));

                return predictedIds.Take(count).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to predict content for warmup");
                return new List<long>();
            }
        }

        public async Task<WarmupResult> WarmupByUserBehaviorAsync()
        {
            // For now, implement as a combination of popular and recent strategies
            var batchResult = await WarmupAllCategoriesAsync(WarmupStrategy.Popular);

            return new WarmupResult
            {
                Strategy = WarmupStrategy.Predictive,
                StartTime = batchResult.StartTime,
                EndTime = batchResult.EndTime,
                Duration = batchResult.TotalDuration,
                TotalItems = batchResult.TotalItemsWarmedUp,
                WarmedUpItems = batchResult.TotalItemsWarmedUp,
                Success = batchResult.OverallSuccessRate > 0.7
            };
        }

        private async Task<List<long>> GetImagesForWarmupAsync(string category, WarmupStrategy strategy, int maxItems)
        {
            try
            {
                switch (strategy)
                {
                    case WarmupStrategy.Popular:
                        // Get most accessed images (would need access tracking)
                        var popularResult = await _galleryRepository.GetCategoryImagesAsync(category, 1, maxItems, "AccessCount", true);
                        return popularResult.Items.Select(img => img.Id).ToList();
                    
                    case WarmupStrategy.Recent:
                        var recentResult = await _galleryRepository.GetCategoryImagesAsync(category, 1, maxItems, "DateUploaded", true);
                        return recentResult.Items.Select(img => img.Id).ToList();
                    
                    case WarmupStrategy.Trending:
                        // For now, use recent as proxy for trending
                        var trendingResult = await _galleryRepository.GetCategoryImagesAsync(category, 1, maxItems, "DateUploaded", true);
                        return trendingResult.Items.Select(img => img.Id).ToList();
                    
                    default:
                        var defaultResult = await _galleryRepository.GetCategoryImagesAsync(category, 1, maxItems, "DateTaken", true);
                        return defaultResult.Items.Select(img => img.Id).ToList();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get images for warmup: {Category}, {Strategy}", category, strategy);
                return new List<long>();
            }
        }
        
        private void AddToHistory(WarmupHistoryEntry entry)
        {
            _warmupHistory.Enqueue(entry);
            
            // Keep history size manageable
            while (_warmupHistory.Count > MAX_HISTORY_ENTRIES)
            {
                _warmupHistory.TryDequeue(out _);
            }
        }

        public async Task<WarmupEffectivenessMetrics> GetEffectivenessMetricsAsync()
        {
            var metrics = new WarmupEffectivenessMetrics
            {
                AnalysisPeriodStart = DateTime.UtcNow.AddDays(-7),
                AnalysisPeriodEnd = DateTime.UtcNow
            };

            try
            {
                var recentHistory = _warmupHistory.Where(h => h.Timestamp >= metrics.AnalysisPeriodStart).ToList();

                if (recentHistory.Any())
                {
                    metrics.TotalWarmupOperations = recentHistory.Count;
                    metrics.SuccessfulWarmupOperations = recentHistory.Count(h => h.Success);
                    metrics.WarmupEfficiencyScore = recentHistory.Average(h => h.EffectivenessScore);

                    // Calculate effectiveness by category
                    var categoryGroups = recentHistory.Where(h => !string.IsNullOrEmpty(h.Category)).GroupBy(h => h.Category!);
                    foreach (var group in categoryGroups)
                    {
                        metrics.EffectivenessByCategory[group.Key] = group.Average(h => h.EffectivenessScore);
                    }

                    // Calculate effectiveness by strategy
                    var strategyGroups = recentHistory.GroupBy(h => h.Strategy);
                    foreach (var group in strategyGroups)
                    {
                        metrics.EffectivenessByStrategy[group.Key] = group.Average(h => h.EffectivenessScore);
                    }

                    // Identify top performing strategies
                    metrics.TopPerformingStrategies = metrics.EffectivenessByStrategy
                        .OrderByDescending(kvp => kvp.Value)
                        .Take(3)
                        .Select(kvp => kvp.Key.ToString())
                        .ToList();
                }

                // Get cache performance improvement (would need baseline comparison)
                var cacheMetrics = await _metricsService.GetCacheMetricsAsync(TimeSpan.FromHours(24));
                metrics.CacheHitRateImprovement = Math.Max(0, cacheMetrics.OverallHitRate - 0.5); // Assume 50% baseline

                return metrics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get warmup effectiveness metrics");
                return metrics;
            }
        }

        public async Task<List<WarmupHistoryEntry>> GetWarmupHistoryAsync(int count = 50)
        {
            return _warmupHistory.OrderByDescending(h => h.Timestamp).Take(count).ToList();
        }

        public async Task<WarmupOptimizationResult> OptimizeWarmupStrategyAsync()
        {
            var result = new WarmupOptimizationResult();

            try
            {
                var effectiveness = await GetEffectivenessMetricsAsync();

                // Find the most effective strategy
                if (effectiveness.EffectivenessByStrategy.Any())
                {
                    var bestStrategy = effectiveness.EffectivenessByStrategy
                        .OrderByDescending(kvp => kvp.Value)
                        .First();

                    result.OptimalStrategy = bestStrategy.Key;
                    result.ExpectedImprovement = bestStrategy.Value - effectiveness.WarmupEfficiencyScore;

                    if (result.ExpectedImprovement > 0.1) // 10% improvement threshold
                    {
                        result.OptimizationApplied = true;
                        result.OptimizationChanges.Add($"Switched to {bestStrategy.Key} strategy for better performance");
                    }
                }

                // Optimize frequency based on cache hit rates
                var cacheMetrics = await _metricsService.GetCacheMetricsAsync(TimeSpan.FromHours(24));
                if (cacheMetrics.OverallHitRate < 0.6)
                {
                    result.OptimalFrequencyHours = 2; // More frequent warmup
                    result.Recommendations.Add("Increase warmup frequency due to low cache hit rate");
                }
                else if (cacheMetrics.OverallHitRate > 0.9)
                {
                    result.OptimalFrequencyHours = 8; // Less frequent warmup
                    result.Recommendations.Add("Reduce warmup frequency due to excellent cache performance");
                }
                else
                {
                    result.OptimalFrequencyHours = 4; // Standard frequency
                }

                // Optimize categories based on effectiveness
                result.OptimalCategories = effectiveness.EffectivenessByCategory
                    .Where(kvp => kvp.Value > 0.7)
                    .OrderByDescending(kvp => kvp.Value)
                    .Select(kvp => kvp.Key)
                    .ToList();

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to optimize warmup strategy");
                result.OptimalStrategy = WarmupStrategy.Popular;
                result.OptimalFrequencyHours = 4;
                return result;
            }
        }

        public async Task<WarmupStatus> GetWarmupStatusAsync()
        {
            return new WarmupStatus
            {
                IsWarmupInProgress = _warmupInProgress,
                CurrentOperation = _currentOperation,
                ActiveSchedules = _activeSchedules.Count(kvp => kvp.Value.IsActive),
                LastWarmupTime = _lastWarmupTime,
                NextScheduledWarmup = _activeSchedules.Values
                    .Where(s => s.IsActive && s.NextRun.HasValue)
                    .OrderBy(s => s.NextRun)
                    .FirstOrDefault()?.NextRun,
                LastWarmupResult = _warmupHistory.OrderByDescending(h => h.Timestamp).FirstOrDefault() != null
                    ? new WarmupResult
                    {
                        Success = _warmupHistory.OrderByDescending(h => h.Timestamp).First().Success,
                        Duration = _warmupHistory.OrderByDescending(h => h.Timestamp).First().Duration,
                        Strategy = _warmupHistory.OrderByDescending(h => h.Timestamp).First().Strategy
                    }
                    : null,
                Statistics = new Dictionary<string, object>
                {
                    ["TotalWarmupOperations"] = _warmupHistory.Count,
                    ["SuccessfulOperations"] = _warmupHistory.Count(h => h.Success),
                    ["AverageEffectiveness"] = _warmupHistory.Any() ? _warmupHistory.Average(h => h.EffectivenessScore) : 0
                }
            };
        }

        public async Task<WarmupHealthCheck> GetHealthCheckAsync()
        {
            var healthCheck = new WarmupHealthCheck();

            try
            {
                // Check if warmup operations are working
                var recentOperations = _warmupHistory.Where(h => h.Timestamp >= DateTime.UtcNow.AddHours(-24)).ToList();
                healthCheck.WarmupOperationsWorking = recentOperations.Any() && recentOperations.Any(h => h.Success);

                if (!healthCheck.WarmupOperationsWorking)
                {
                    healthCheck.Issues.Add("No successful warmup operations in the last 24 hours");
                }

                // Check scheduler
                healthCheck.SchedulerWorking = _activeSchedules.Any(kvp => kvp.Value.IsActive);

                // Count failed operations
                healthCheck.FailedOperations = recentOperations.Count(h => !h.Success);

                // Get last successful warmup
                healthCheck.LastSuccessfulWarmup = _warmupHistory
                    .Where(h => h.Success)
                    .OrderByDescending(h => h.Timestamp)
                    .FirstOrDefault()?.Timestamp;

                // Overall health
                healthCheck.IsHealthy = healthCheck.WarmupOperationsWorking && healthCheck.FailedOperations < 5;
                healthCheck.Status = healthCheck.IsHealthy ? "Healthy" : "Unhealthy";

                // Add metrics
                healthCheck.Metrics["ActiveSchedules"] = _activeSchedules.Count(kvp => kvp.Value.IsActive);
                healthCheck.Metrics["RecentOperations"] = recentOperations.Count;
                healthCheck.Metrics["SuccessRate"] = recentOperations.Any() ? (double)recentOperations.Count(h => h.Success) / recentOperations.Count : 0;

                return healthCheck;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Warmup health check failed");
                healthCheck.IsHealthy = false;
                healthCheck.Status = "Error";
                healthCheck.Issues.Add($"Health check error: {ex.Message}");
                return healthCheck;
            }
        }
    }
}
