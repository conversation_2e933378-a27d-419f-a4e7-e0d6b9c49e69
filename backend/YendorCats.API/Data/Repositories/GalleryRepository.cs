using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using YendorCats.API.Data;
using YendorCats.API.Models;
using YendorCats.API.Models.DTOs;

namespace YendorCats.API.Data.Repositories
{
    /// <summary>
    /// High-performance gallery repository implementation with dual storage support
    /// Optimized for <200ms response times with strategic indexes and batch operations
    /// </summary>
    public class GalleryRepository : IGalleryRepository
    {
        private readonly AppDbContext _context;
        private readonly ILogger<GalleryRepository> _logger;

        public GalleryRepository(AppDbContext context, ILogger<GalleryRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        // Core CRUD operations
        
        public async Task<CatGalleryImage?> GetByIdAsync(long id)
        {
            return await _context.CatGalleryImages
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == id);
        }
        
        public async Task<CatGalleryImage?> GetByStorageKeyAsync(string storageKey)
        {
            return await _context.CatGalleryImages
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.StorageKey == storageKey);
        }
        
        public async Task<CatGalleryImage?> GetByFilenameAsync(string filename)
        {
            return await _context.CatGalleryImages
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Filename == filename);
        }
        
        public async Task<List<CatGalleryImage>> GetByFilenamesAsync(IEnumerable<string> filenames)
        {
            var filenameList = filenames.ToList();
            return await _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => filenameList.Contains(i.Filename))
                .ToListAsync();
        }
        

        
        public async Task<CatGalleryImage?> GetByB2KeyAsync(string b2Key)
        {
            return await _context.CatGalleryImages
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.B2Key == b2Key);
        }
        
        public async Task<bool> ExistsAsync(string filename)
        {
            return await _context.CatGalleryImages
                .AsNoTracking()
                .AnyAsync(i => i.Filename == filename);
        }
        
        public async Task<bool> ExistsByS3KeyAsync(string s3Key)
        {
            return await _context.CatGalleryImages
                .AsNoTracking()
                .AnyAsync(i => i.S3Key == s3Key);
        }
        
        public async Task<bool> ExistsByB2KeyAsync(string b2Key)
        {
            return await _context.CatGalleryImages
                .AsNoTracking()
                .AnyAsync(i => i.B2Key == b2Key);
        }
        
        public async Task<CatGalleryImage> AddAsync(CatGalleryImage image)
        {
            image.CreatedAt = DateTime.UtcNow;
            image.UpdatedAt = DateTime.UtcNow;
            
            _context.CatGalleryImages.Add(image);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Added new gallery image: {Filename}", image.Filename);
            return image;
        }
        
        public async Task<CatGalleryImage> UpdateAsync(CatGalleryImage image)
        {
            image.UpdatedAt = DateTime.UtcNow;
            
            _context.CatGalleryImages.Update(image);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Updated gallery image: {Filename}", image.Filename);
            return image;
        }
        
        public async Task<bool> DeleteAsync(long id)
        {
            var image = await _context.CatGalleryImages.FindAsync(id);
            if (image == null)
                return false;
            
            _context.CatGalleryImages.Remove(image);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Deleted gallery image: {Filename}", image.Filename);
            return true;
        }
        
        public async Task<List<CatGalleryImage>> BulkUpdateAsync(IEnumerable<CatGalleryImage> images)
        {
            var imageList = images.ToList();
            var utcNow = DateTime.UtcNow;
            
            foreach (var image in imageList)
            {
                image.UpdatedAt = utcNow;
            }
            
            _context.CatGalleryImages.UpdateRange(imageList);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Bulk updated {Count} gallery images", imageList.Count);
            return imageList;
        }
        
        public async Task<List<CatGalleryImage>> BulkInsertAsync(IEnumerable<CatGalleryImage> images)
        {
            var imageList = images.ToList();
            var utcNow = DateTime.UtcNow;
            
            foreach (var image in imageList)
            {
                image.CreatedAt = utcNow;
                image.UpdatedAt = utcNow;
            }
            
            _context.CatGalleryImages.AddRange(imageList);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Bulk inserted {Count} gallery images", imageList.Count);
            return imageList;
        }
        
        // Listing and pagination with performance optimization
        
        public async Task<PagedResult<CatGalleryImage>> GetAllAsync(
            int page = 1,
            int pageSize = 20,
            string sortBy = "DisplayOrder",
            bool descending = false,
            bool activeOnly = true,
            bool publicOnly = true,
            bool featuredOnly = false)
        {
            var query = _context.CatGalleryImages.AsNoTracking();
            
            // Apply filters
            if (activeOnly)
                query = query.Where(i => i.IsActive);
            
            if (publicOnly)
                query = query.Where(i => i.IsPublic);
            
            if (featuredOnly)
                query = query.Where(i => i.IsFeatured);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatGalleryImage>> GetByCatIdAsync(
            string catId,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true,
            bool activeOnly = false,
            bool publicOnly = false)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.CatId == catId);
            
            // Apply filters
            if (activeOnly)
                query = query.Where(i => i.IsActive);
            
            if (publicOnly)
                query = query.Where(i => i.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatGalleryImage>> GetByCatIdsAsync(
            IEnumerable<string> catIds,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DisplayOrder",
            bool descending = false,
            bool activeOnly = true,
            bool publicOnly = true)
        {
            var catIdList = catIds.ToList();
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => catIdList.Contains(i.CatId));
            
            // Apply filters
            if (activeOnly)
                query = query.Where(i => i.IsActive);
            
            if (publicOnly)
                query = query.Where(i => i.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatGalleryImage>> GetByStorageProviderAsync(
            string storageProvider,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DisplayOrder",
            bool descending = false,
            bool activeOnly = true,
            bool publicOnly = true)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.StorageProvider == storageProvider);
            
            // Apply filters
            if (activeOnly)
                query = query.Where(i => i.IsActive);
            
            if (publicOnly)
                query = query.Where(i => i.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        // Search functionality with full-text search optimization
        
        public async Task<PagedResult<CatGalleryImage>> SearchAsync(
            string query,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DisplayOrder",
            bool descending = false,
            bool activeOnly = true,
            bool publicOnly = true)
        {
            var searchQuery = _context.CatGalleryImages.AsNoTracking();
            
            // Apply search filters
            if (!string.IsNullOrWhiteSpace(query))
            {
                var searchTerm = query.ToLower();
                searchQuery = searchQuery.Where(i => 
                    i.Filename.ToLower().Contains(searchTerm) ||
                    i.CatId.ToLower().Contains(searchTerm) ||
                    (i.Title != null && i.Title.ToLower().Contains(searchTerm)) ||
                    (i.Description != null && i.Description.ToLower().Contains(searchTerm)) ||
                    (i.Tags != null && i.Tags.ToLower().Contains(searchTerm)) ||
                    (i.Alt != null && i.Alt.ToLower().Contains(searchTerm)));
            }
            
            // Apply filters
            if (activeOnly)
                searchQuery = searchQuery.Where(i => i.IsActive);
            
            if (publicOnly)
                searchQuery = searchQuery.Where(i => i.IsPublic);
            
            // Apply sorting
            searchQuery = ApplySorting(searchQuery, sortBy, descending);
            
            // Get total count
            var totalCount = await searchQuery.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await searchQuery.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatGalleryImage>> GetByTagsAsync(
            IEnumerable<string> tags,
            bool matchAll = false,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DisplayOrder",
            bool descending = false,
            bool activeOnly = true,
            bool publicOnly = true)
        {
            var tagList = tags.Select(t => t.ToLower()).ToList();
            var query = _context.CatGalleryImages.AsNoTracking();
            
            // Apply tag filtering
            if (matchAll)
            {
                // All tags must be present
                foreach (var tag in tagList)
                {
                    query = query.Where(i => i.Tags != null && i.Tags.ToLower().Contains(tag));
                }
            }
            else
            {
                // Any tag must be present
                query = query.Where(i => i.Tags != null && tagList.Any(tag => i.Tags.ToLower().Contains(tag)));
            }
            
            // Apply filters
            if (activeOnly)
                query = query.Where(i => i.IsActive);
            
            if (publicOnly)
                query = query.Where(i => i.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatGalleryImage>> GetByDateRangeAsync(
            DateTime startDate,
            DateTime endDate,
            int page = 1,
            int pageSize = 20,
            string sortBy = "CreatedAt",
            bool descending = true,
            bool activeOnly = true,
            bool publicOnly = true)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.CreatedAt >= startDate && i.CreatedAt <= endDate);
            
            // Apply filters
            if (activeOnly)
                query = query.Where(i => i.IsActive);
            
            if (publicOnly)
                query = query.Where(i => i.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        // Specialized queries for performance
        
        public async Task<PagedResult<CatGalleryImage>> GetFeaturedAsync(
            int page = 1,
            int pageSize = 20,
            string sortBy = "DisplayOrder",
            bool descending = false,
            bool activeOnly = true,
            bool publicOnly = true)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.IsFeatured);
            
            // Apply filters
            if (activeOnly)
                query = query.Where(i => i.IsActive);
            
            if (publicOnly)
                query = query.Where(i => i.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<List<CatGalleryImage>> GetRecentAsync(
            int count = 10,
            bool activeOnly = true,
            bool publicOnly = true)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .OrderByDescending(i => i.CreatedAt);
            
            // Apply filters
            if (activeOnly)
                query = query.Where(i => i.IsActive);
            
            if (publicOnly)
                query = query.Where(i => i.IsPublic);
            
            return await query.Take(count).ToListAsync();
        }
        
        public async Task<List<CatGalleryImage>> GetPopularAsync(
            int count = 10,
            bool activeOnly = true,
            bool publicOnly = true)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .OrderByDescending(i => i.ViewCount)
                .ThenByDescending(i => i.LikeCount);
            
            // Apply filters
            if (activeOnly)
                query = query.Where(i => i.IsActive);
            
            if (publicOnly)
                query = query.Where(i => i.IsPublic);
            
            return await query.Take(count).ToListAsync();
        }
        
        public async Task<List<CatGalleryImage>> GetRandomAsync(
            int count = 10,
            bool activeOnly = true,
            bool publicOnly = true)
        {
            var query = _context.CatGalleryImages.AsNoTracking();
            
            // Apply filters
            if (activeOnly)
                query = query.Where(i => i.IsActive);
            
            if (publicOnly)
                query = query.Where(i => i.IsPublic);
            
            // Get random selection using GUID ordering (SQL Server compatible)
            return await query.OrderBy(i => Guid.NewGuid()).Take(count).ToListAsync();
        }
        
        // Statistics and analytics
        
        public async Task<Dictionary<string, int>> GetStorageProviderStatsAsync()
        {
            return await _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.IsActive)
                .GroupBy(i => i.StorageProvider)
                .ToDictionaryAsync(g => g.Key, g => g.Count());
        }
        
        public async Task<Dictionary<string, int>> GetCatStatsAsync()
        {
            return await _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.IsActive && i.IsPublic)
                .GroupBy(i => i.CatId)
                .ToDictionaryAsync(g => g.Key, g => g.Count());
        }
        
        public async Task<Dictionary<string, int>> GetTagStatsAsync()
        {
            var images = await _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.IsActive && i.IsPublic && i.Tags != null)
                .Select(i => i.Tags)
                .ToListAsync();
            
            var tagStats = new Dictionary<string, int>();
            
            foreach (var tagString in images)
            {
                if (string.IsNullOrWhiteSpace(tagString)) continue;
                
                var tags = tagString.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(t => t.Trim().ToLower())
                    .Where(t => !string.IsNullOrWhiteSpace(t));
                
                foreach (var tag in tags)
                {
                    tagStats[tag] = tagStats.GetValueOrDefault(tag, 0) + 1;
                }
            }
            
            return tagStats.OrderByDescending(kvp => kvp.Value).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }
        
        public async Task<int> GetTotalCountAsync(bool activeOnly = true, bool publicOnly = true)
        {
            var query = _context.CatGalleryImages.AsNoTracking();
            
            if (activeOnly)
                query = query.Where(i => i.IsActive);
            
            if (publicOnly)
                query = query.Where(i => i.IsPublic);
            
            return await query.CountAsync();
        }
        
        public async Task<int> GetCountByCatIdAsync(string catId, bool activeOnly = true, bool publicOnly = true)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.CatId == catId);
            
            if (activeOnly)
                query = query.Where(i => i.IsActive);
            
            if (publicOnly)
                query = query.Where(i => i.IsPublic);
            
            return await query.CountAsync();
        }
        
        public async Task<int> GetCountByStorageProviderAsync(string storageProvider, bool activeOnly = true)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.StorageProvider == storageProvider);
            
            if (activeOnly)
                query = query.Where(i => i.IsActive);
            
            return await query.CountAsync();
        }
        
        // Bulk operations for performance
        
        public async Task<bool> BulkUpdateDisplayOrderAsync(Dictionary<int, int> imageDisplayOrders)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                var imageIds = imageDisplayOrders.Keys.ToList();
                var images = await _context.CatGalleryImages
                    .Where(i => imageIds.Contains(i.Id))
                    .ToListAsync();
                
                foreach (var image in images)
                {
                    if (imageDisplayOrders.TryGetValue(image.Id, out var displayOrder))
                    {
                        image.DisplayOrder = displayOrder;
                        image.UpdatedAt = DateTime.UtcNow;
                    }
                }
                
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                
                _logger.LogInformation("Bulk updated display order for {Count} images", images.Count);
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Failed to bulk update display orders");
                return false;
            }
        }
        
        public async Task<bool> BulkUpdateTagsAsync(Dictionary<int, string> imageTags)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                var imageIds = imageTags.Keys.ToList();
                var images = await _context.CatGalleryImages
                    .Where(i => imageIds.Contains(i.Id))
                    .ToListAsync();
                
                foreach (var image in images)
                {
                    if (imageTags.TryGetValue(image.Id, out var tags))
                    {
                        image.Tags = tags;
                        image.UpdatedAt = DateTime.UtcNow;
                    }
                }
                
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                
                _logger.LogInformation("Bulk updated tags for {Count} images", images.Count);
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Failed to bulk update tags");
                return false;
            }
        }
        
        public async Task<bool> BulkUpdateVisibilityAsync(IEnumerable<int> imageIds, bool isActive, bool isPublic)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                var ids = imageIds.ToList();
                var images = await _context.CatGalleryImages
                    .Where(i => ids.Contains(i.Id))
                    .ToListAsync();
                
                foreach (var image in images)
                {
                    image.IsActive = isActive;
                    image.IsPublic = isPublic;
                    image.UpdatedAt = DateTime.UtcNow;
                }
                
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                
                _logger.LogInformation("Bulk updated visibility for {Count} images", images.Count);
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Failed to bulk update visibility");
                return false;
            }
        }
        
        // Migration support methods
        
        public async Task<PagedResult<CatGalleryImage>> GetImagesNeedingB2SyncAsync(
            int page = 1,
            int pageSize = 100,
            string sortBy = "CreatedAt",
            bool descending = false)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.StorageProvider == "S3" || string.IsNullOrEmpty(i.B2Key));
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatGalleryImage>> GetImagesForMigrationAsync(
            string sourceProvider,
            int page = 1,
            int pageSize = 100,
            string sortBy = "CreatedAt",
            bool descending = false)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.StorageProvider == sourceProvider);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<MigrationBatch> GetMigrationBatchAsync(
            string sourceProvider,
            int batchSize = 100,
            int skip = 0)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.StorageProvider == sourceProvider)
                .OrderBy(i => i.CreatedAt);
            
            var totalCount = await query.CountAsync();
            var items = await query.Skip(skip).Take(batchSize).ToListAsync();
            
            return new MigrationBatch
            {
                Items = items,
                BatchSize = batchSize,
                Skip = skip,
                TotalCount = totalCount,
                HasMore = skip + batchSize < totalCount,
                NextSkip = skip + batchSize
            };
        }
        
        // Missing interface methods implementation
        
        public async Task<PagedResult<CatGalleryImage>> GetCategoryImagesAsync(
            string category, 
            int page = 1, 
            int pageSize = 20, 
            string sortBy = "DateTaken", 
            bool descending = true,
            bool activeOnly = true,
            bool publicOnly = true)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.Category == category);
            
            if (activeOnly) query = query.Where(i => i.IsActive);
            if (publicOnly) query = query.Where(i => i.IsPublic);
            
            query = ApplySorting(query, sortBy, descending);
            
            var totalCount = await query.CountAsync();
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatGalleryImage>> GetMultipleCategoriesAsync(
            IEnumerable<string> categories,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true)
        {
            var categoryList = categories.ToList();
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => categoryList.Contains(i.Category) && i.IsActive && i.IsPublic);
            
            query = ApplySorting(query, sortBy, descending);
            
            var totalCount = await query.CountAsync();
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatGalleryImage>> GetByCatNameAsync(
            string catName,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.CatName == catName && i.IsActive && i.IsPublic);
            
            query = ApplySorting(query, sortBy, descending);
            
            var totalCount = await query.CountAsync();
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatGalleryImage>> GetByBreedAsync(
            string breed,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.Breed == breed && i.IsActive && i.IsPublic);
            
            query = ApplySorting(query, sortBy, descending);
            
            var totalCount = await query.CountAsync();
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatGalleryImage>> GetByBloodlineAsync(
            string bloodline,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.Bloodline == bloodline && i.IsActive && i.IsPublic);
            
            query = ApplySorting(query, sortBy, descending);
            
            var totalCount = await query.CountAsync();
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<int> GetCategoryCountAsync(string category, bool activeOnly = true, bool publicOnly = true)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.Category == category);
            
            if (activeOnly) query = query.Where(i => i.IsActive);
            if (publicOnly) query = query.Where(i => i.IsPublic);
            
            return await query.CountAsync();
        }
        
        public async Task<Dictionary<string, int>> GetCategoryStatsAsync(bool activeOnly = true, bool publicOnly = true)
        {
            var query = _context.CatGalleryImages.AsNoTracking();
            
            if (activeOnly) query = query.Where(i => i.IsActive);
            if (publicOnly) query = query.Where(i => i.IsPublic);
            
            return await query
                .GroupBy(i => i.Category)
                .ToDictionaryAsync(g => g.Key, g => g.Count());
        }
        
        public async Task<List<CatGalleryImage>> GetRecentlyAccessedAsync(int count = 10, string? category = null)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.IsActive && i.IsPublic && i.LastAccessedAt != null)
                .OrderByDescending(i => i.LastAccessedAt);
            
            if (!string.IsNullOrEmpty(category))
                query = query.Where(i => i.Category == category);
            
            return await query.Take(count).ToListAsync();
        }
        
        public async Task<List<CatGalleryImage>> GetPopularImagesAsync(int count = 10, string? category = null, TimeSpan? timeRange = null)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.IsActive && i.IsPublic);
            
            if (!string.IsNullOrEmpty(category))
                query = query.Where(i => i.Category == category);
            
            if (timeRange.HasValue)
            {
                var cutoffDate = DateTime.UtcNow.Subtract(timeRange.Value);
                query = query.Where(i => i.LastAccessedAt >= cutoffDate);
            }
            
            return await query
                .OrderByDescending(i => i.ViewCount)
                .ThenByDescending(i => i.LikeCount)
                .Take(count)
                .ToListAsync();
        }
        
        public async Task<List<CatGalleryImage>> GetRecentlyUploadedAsync(int count = 10, string? category = null)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.IsActive && i.IsPublic)
                .OrderByDescending(i => i.CreatedAt);
            
            if (!string.IsNullOrEmpty(category))
                query = query.Where(i => i.Category == category);
            
            return await query.Take(count).ToListAsync();
        }
        
        public async Task<List<CatGalleryImage>> GetFeaturedImagesAsync(int count = 10, string? category = null)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.IsActive && i.IsPublic && i.IsFeatured)
                .OrderBy(i => i.DisplayOrder);
            
            if (!string.IsNullOrEmpty(category))
                query = query.Where(i => i.Category == category);
            
            return await query.Take(count).ToListAsync();
        }
        
        public async Task<Dictionary<string, int>> GetBreedStatsAsync()
        {
            return await _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.IsActive && i.IsPublic && !string.IsNullOrEmpty(i.Breed))
                .GroupBy(i => i.Breed)
                .ToDictionaryAsync(g => g.Key, g => g.Count());
        }
        
        public async Task<Dictionary<string, int>> GetBloodlineStatsAsync()
        {
            return await _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.IsActive && i.IsPublic && !string.IsNullOrEmpty(i.Bloodline))
                .GroupBy(i => i.Bloodline)
                .ToDictionaryAsync(g => g.Key, g => g.Count());
        }
        
        public async Task<bool> IncrementAccessCountAsync(long id)
        {
            var image = await _context.CatGalleryImages.FindAsync(id);
            if (image == null) return false;
            
            image.AccessCount++;
            image.ViewCount++;
            image.LastAccessedAt = DateTime.UtcNow;
            
            await _context.SaveChangesAsync();
            return true;
        }
        
        public async Task<bool> UpdateLastAccessedAsync(long id)
        {
            var image = await _context.CatGalleryImages.FindAsync(id);
            if (image == null) return false;
            
            image.LastAccessedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            return true;
        }
        
        public async Task<List<CatGalleryImage>> GetImagesForCacheWarmupAsync(string? category = null, int count = 100)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.IsActive && i.IsPublic)
                .OrderByDescending(i => i.ViewCount)
                .ThenByDescending(i => i.LastAccessedAt);
            
            if (!string.IsNullOrEmpty(category))
                query = query.Where(i => i.Category == category);
            
            return await query.Take(count).ToListAsync();
        }
        
        public async Task<bool> UpdateSortOrdersAsync(Dictionary<long, int> imageIds)
        {
            var ids = imageIds.Keys.ToList();
            var images = await _context.CatGalleryImages
                .Where(i => ids.Contains(i.Id))
                .ToListAsync();
            
            foreach (var image in images)
            {
                if (imageIds.TryGetValue(image.Id, out var sortOrder))
                {
                    image.SortOrder = sortOrder;
                    image.UpdatedAt = DateTime.UtcNow;
                }
            }
            
            await _context.SaveChangesAsync();
            return true;
        }
        
        public async Task<List<List<CatGalleryImage>>> GetDuplicateImagesAsync(string? category = null)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.IsActive);
            
            if (!string.IsNullOrEmpty(category))
                query = query.Where(i => i.Category == category);
            
            var groups = await query
                .GroupBy(i => new { i.FileSize, i.Width, i.Height })
                .Where(g => g.Count() > 1)
                .Select(g => g.ToList())
                .ToListAsync();
            
            return groups;
        }
        
        public async Task<PagedResult<CatGalleryImage>> GetImagesWithMissingMetadataAsync(
            string? category = null,
            int page = 1,
            int pageSize = 20)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.IsActive && 
                           (string.IsNullOrEmpty(i.Title) || 
                            string.IsNullOrEmpty(i.Description) || 
                            string.IsNullOrEmpty(i.Alt)));
            
            if (!string.IsNullOrEmpty(category))
                query = query.Where(i => i.Category == category);
            
            var totalCount = await query.CountAsync();
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<List<string>> GetStorageKeysForMigrationAsync(
            string sourceProvider,
            int batchSize = 100,
            int skip = 0)
        {
            return await _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.StorageProvider == sourceProvider)
                .OrderBy(i => i.CreatedAt)
                .Skip(skip)
                .Take(batchSize)
                .Select(i => i.StorageKey)
                .ToListAsync();
        }
        
        public async Task<List<CatGalleryImage>> GetImagesWithoutThumbnailsAsync(string? category = null, int limit = 100)
        {
            var query = _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.IsActive && string.IsNullOrEmpty(i.ThumbnailStorageKey));
            
            if (!string.IsNullOrEmpty(category))
                query = query.Where(i => i.Category == category);
            
            return await query.Take(limit).ToListAsync();
        }
        
        public async Task<int> GetImagesWithThumbnailsCountAsync()
        {
            return await _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.IsActive && !string.IsNullOrEmpty(i.ThumbnailStorageKey))
                .CountAsync();
        }
        
        public async Task<List<CatGalleryImage>> GetRecentlyAccessedImagesAsync(DateTime since, int limit = 100)
        {
            return await _context.CatGalleryImages
                .AsNoTracking()
                .Where(i => i.IsActive && i.LastAccessedAt >= since)
                .OrderByDescending(i => i.LastAccessedAt)
                .Take(limit)
                .ToListAsync();
        }
        
        // Helper methods
        
        private IQueryable<CatGalleryImage> ApplySorting(IQueryable<CatGalleryImage> query, string sortBy, bool descending)
        {
            return sortBy.ToLower() switch
            {
                "filename" => descending ? query.OrderByDescending(i => i.Filename) : query.OrderBy(i => i.Filename),
                "createdat" => descending ? query.OrderByDescending(i => i.CreatedAt) : query.OrderBy(i => i.CreatedAt),
                "updatedat" => descending ? query.OrderByDescending(i => i.UpdatedAt) : query.OrderBy(i => i.UpdatedAt),
                "datetaken" => descending ? query.OrderByDescending(i => i.DateTaken) : query.OrderBy(i => i.DateTaken),
                "filesize" => descending ? query.OrderByDescending(i => i.FileSize) : query.OrderBy(i => i.FileSize),
                "viewcount" => descending ? query.OrderByDescending(i => i.ViewCount) : query.OrderBy(i => i.ViewCount),
                "likecount" => descending ? query.OrderByDescending(i => i.LikeCount) : query.OrderBy(i => i.LikeCount),
                "catid" => descending ? query.OrderByDescending(i => i.CatId) : query.OrderBy(i => i.CatId),
                "catname" => descending ? query.OrderByDescending(i => i.CatName) : query.OrderBy(i => i.CatName),
                "displayorder" => descending ? query.OrderByDescending(i => i.DisplayOrder) : query.OrderBy(i => i.DisplayOrder),
                _ => descending ? query.OrderByDescending(i => i.DisplayOrder) : query.OrderBy(i => i.DisplayOrder)
            };
        }

        // Missing interface methods with correct signatures

        public async Task<PagedResult<CatGalleryImage>> SearchAsync(
            string query,
            string? category = null,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true)
        {
            var queryable = _context.CatGalleryImages.AsNoTracking()
                .Where(i => i.IsActive && i.IsPublic);

            // Apply category filter
            if (!string.IsNullOrEmpty(category))
                queryable = queryable.Where(i => i.Category == category);

            // Apply search term
            if (!string.IsNullOrEmpty(query))
            {
                var lowerQuery = query.ToLower();
                queryable = queryable.Where(i =>
                    i.Filename.ToLower().Contains(lowerQuery) ||
                    i.CatName.ToLower().Contains(lowerQuery) ||
                    i.CatId.ToLower().Contains(lowerQuery) ||
                    (i.Tags != null && i.Tags.ToLower().Contains(lowerQuery)) ||
                    (i.Description != null && i.Description.ToLower().Contains(lowerQuery)));
            }

            // Apply sorting
            queryable = sortBy.ToLower() switch
            {
                "filename" => descending ? queryable.OrderByDescending(i => i.Filename) : queryable.OrderBy(i => i.Filename),
                "datetaken" => descending ? queryable.OrderByDescending(i => i.DateTaken) : queryable.OrderBy(i => i.DateTaken),
                "createdat" => descending ? queryable.OrderByDescending(i => i.CreatedAt) : queryable.OrderBy(i => i.CreatedAt),
                _ => descending ? queryable.OrderByDescending(i => i.DateTaken) : queryable.OrderBy(i => i.DateTaken)
            };

            var totalCount = await queryable.CountAsync();
            var items = await queryable.Skip((page - 1) * pageSize).Take(pageSize).ToListAsync();

            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };
        }

        public async Task<PagedResult<CatGalleryImage>> GetByDateRangeAsync(
            DateTime startDate,
            DateTime endDate,
            string? category = null,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true)
        {
            var queryable = _context.CatGalleryImages.AsNoTracking()
                .Where(i => i.IsActive && i.IsPublic && i.DateTaken >= startDate && i.DateTaken <= endDate);

            if (!string.IsNullOrEmpty(category))
                queryable = queryable.Where(i => i.Category == category);

            // Apply sorting
            queryable = sortBy.ToLower() switch
            {
                "filename" => descending ? queryable.OrderByDescending(i => i.Filename) : queryable.OrderBy(i => i.Filename),
                "datetaken" => descending ? queryable.OrderByDescending(i => i.DateTaken) : queryable.OrderBy(i => i.DateTaken),
                "createdat" => descending ? queryable.OrderByDescending(i => i.CreatedAt) : queryable.OrderBy(i => i.CreatedAt),
                _ => descending ? queryable.OrderByDescending(i => i.DateTaken) : queryable.OrderBy(i => i.DateTaken)
            };

            var totalCount = await queryable.CountAsync();
            var items = await queryable.Skip((page - 1) * pageSize).Take(pageSize).ToListAsync();

            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };
        }

        public async Task<PagedResult<CatGalleryImage>> GetByTagsAsync(
            IEnumerable<string> tags,
            bool matchAll = false,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true)
        {
            var tagList = tags.Select(t => t.ToLower()).ToList();
            var queryable = _context.CatGalleryImages.AsNoTracking()
                .Where(i => i.IsActive && i.IsPublic && i.Tags != null);

            if (matchAll)
            {
                // All tags must be present
                foreach (var tag in tagList)
                {
                    queryable = queryable.Where(i => i.Tags!.ToLower().Contains(tag));
                }
            }
            else
            {
                // Any tag must be present
                queryable = queryable.Where(i => tagList.Any(tag => i.Tags!.ToLower().Contains(tag)));
            }

            // Apply sorting
            queryable = sortBy.ToLower() switch
            {
                "filename" => descending ? queryable.OrderByDescending(i => i.Filename) : queryable.OrderBy(i => i.Filename),
                "datetaken" => descending ? queryable.OrderByDescending(i => i.DateTaken) : queryable.OrderBy(i => i.DateTaken),
                "createdat" => descending ? queryable.OrderByDescending(i => i.CreatedAt) : queryable.OrderBy(i => i.CreatedAt),
                _ => descending ? queryable.OrderByDescending(i => i.DateTaken) : queryable.OrderBy(i => i.DateTaken)
            };

            var totalCount = await queryable.CountAsync();
            var items = await queryable.Skip((page - 1) * pageSize).Take(pageSize).ToListAsync();

            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };
        }

        public async Task<PagedResult<CatGalleryImage>> GetByStorageProviderAsync(
            string storageProvider,
            int page = 1,
            int pageSize = 20)
        {
            var queryable = _context.CatGalleryImages.AsNoTracking()
                .Where(i => i.StorageProvider == storageProvider);

            var totalCount = await queryable.CountAsync();
            var items = await queryable.Skip((page - 1) * pageSize).Take(pageSize).ToListAsync();

            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };
        }

        public async Task<int> GetTotalCountAsync()
        {
            return await _context.CatGalleryImages.AsNoTracking().CountAsync();
        }

        public async Task<int> GetTotalCountAsync(bool activeOnly)
        {
            var query = _context.CatGalleryImages.AsNoTracking();

            if (activeOnly)
                query = query.Where(i => i.IsActive);

            return await query.CountAsync();
        }

        public async Task<List<CatGalleryImage>> GetAllAsync()
        {
            return await _context.CatGalleryImages.AsNoTracking().ToListAsync();
        }

        public async Task<List<CatGalleryImage>> GetAllAsync(bool activeOnly, bool publicOnly = false)
        {
            var query = _context.CatGalleryImages.AsNoTracking();

            if (activeOnly)
                query = query.Where(i => i.IsActive);

            if (publicOnly)
                query = query.Where(i => i.IsPublic);

            return await query.ToListAsync();
        }



        public async Task<CatGalleryImage?> GetByS3KeyAsync(string s3Key)
        {
            return await _context.CatGalleryImages.AsNoTracking()
                .FirstOrDefaultAsync(i => i.StorageKey == s3Key);
        }

        public async Task<int> GetCountByCatIdAsync(string catId)
        {
            return await _context.CatGalleryImages.AsNoTracking()
                .Where(i => i.CatId == catId)
                .CountAsync();
        }

        public async Task<List<CatGalleryImage>> GetImagesNeedingB2SyncAsync(int batchSize = 100)
        {
            return await _context.CatGalleryImages.AsNoTracking()
                .Where(i => i.IsActive && string.IsNullOrEmpty(i.B2Key))
                .Take(batchSize)
                .ToListAsync();
        }

        public async Task<Dictionary<string, object>> GetHealthStatsAsync()
        {
            var totalCount = await _context.CatGalleryImages.CountAsync();
            var activeCount = await _context.CatGalleryImages.Where(i => i.IsActive).CountAsync();
            var publicCount = await _context.CatGalleryImages.Where(i => i.IsPublic).CountAsync();
            var withThumbnails = await _context.CatGalleryImages.Where(i => !string.IsNullOrEmpty(i.ThumbnailStorageKey)).CountAsync();

            return new Dictionary<string, object>
            {
                ["TotalImages"] = totalCount,
                ["ActiveImages"] = activeCount,
                ["PublicImages"] = publicCount,
                ["ImagesWithThumbnails"] = withThumbnails,
                ["HealthScore"] = totalCount > 0 ? (double)activeCount / totalCount * 100 : 0
            };
        }

        public async Task<bool> SoftDeleteAsync(long imageId)
        {
            var image = await _context.CatGalleryImages.FindAsync(imageId);
            if (image != null)
            {
                image.IsActive = false;
                image.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }

        public async Task<List<CatGalleryImage>> GetByIdsAsync(IEnumerable<long> imageIds)
        {
            var ids = imageIds.ToList();
            return await _context.CatGalleryImages.AsNoTracking()
                .Where(i => ids.Contains(i.Id))
                .ToListAsync();
        }

        public async Task<PagedResult<CatGalleryImage>> GetCategoryImagesLightweightAsync(string category, int page = 1, int pageSize = 20)
        {
            var query = _context.CatGalleryImages.AsNoTracking()
                .Where(i => i.IsActive && i.IsPublic && i.Category == category)
                .Select(i => new CatGalleryImage
                {
                    Id = i.Id,
                    Filename = i.Filename,
                    Category = i.Category,
                    CatId = i.CatId,
                    CatName = i.CatName,
                    ThumbnailStorageKey = i.ThumbnailStorageKey,
                    CreatedAt = i.CreatedAt
                });

            var totalCount = await query.CountAsync();
            var items = await query.Skip((page - 1) * pageSize).Take(pageSize).ToListAsync();

            return new PagedResult<CatGalleryImage>
            {
                Items = items,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };
        }

        public async Task<PagedResult<CatGalleryImage>> AdvancedSearchAsync(object searchCriteria)
        {
            // For now, return empty result - this would need proper implementation based on search criteria structure
            return new PagedResult<CatGalleryImage>
            {
                Items = new List<CatGalleryImage>(),
                TotalCount = 0,
                Page = 1,
                PageSize = 20,
                TotalPages = 0
            };
        }

        public async Task<List<CatGalleryImage>> GetRandomImagesAsync(int count, string? category = null)
        {
            var query = _context.CatGalleryImages.AsNoTracking()
                .Where(i => i.IsActive && i.IsPublic);

            if (!string.IsNullOrEmpty(category))
                query = query.Where(i => i.Category == category);

            // Simple random implementation - in production you might want a more efficient approach
            var totalCount = await query.CountAsync();
            if (totalCount == 0) return new List<CatGalleryImage>();

            var random = new Random();
            var skip = random.Next(0, Math.Max(0, totalCount - count));

            return await query.Skip(skip).Take(count).ToListAsync();
        }

        public async Task<List<CatGalleryImage>> GetImagesByCatIdAsync(string catId, bool includeInactive = false)
        {
            var query = _context.CatGalleryImages.AsNoTracking()
                .Where(i => i.CatId == catId);

            if (!includeInactive)
                query = query.Where(i => i.IsActive);

            return await query.ToListAsync();
        }
    }

    /// <summary>
    /// Migration batch result for processing large datasets
    /// </summary>
    public class MigrationBatch
    {
        public List<CatGalleryImage> Items { get; set; } = new();
        public int BatchSize { get; set; }
        public int Skip { get; set; }
        public int TotalCount { get; set; }
        public bool HasMore { get; set; }
        public int NextSkip { get; set; }
    }
}
